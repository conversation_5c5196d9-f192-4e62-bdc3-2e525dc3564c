import { useRef, useEffect } from "preact/hooks";

export interface LoadMoreProps {
  onLoad: () => void;
  loading: boolean;
  /**
   * finished 优先级高于 loading
   */
  finished: boolean;
}

export function LoadMore({ loading, finished, onLoad }: LoadMoreProps) {
  const elRef = useRef<HTMLDivElement>(null);
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting) {
          if (!loading && !finished) {
            onLoad();
          }
        }
      },
      { threshold: 0 }
    );
    if (elRef.current) observer.observe(elRef.current);
    return () => observer.disconnect();
  }, [loading, finished, onLoad]);

  return (
    <div ref={elRef} className="inline-flex items-center">
      <span className="inline-block h-[1.5px] w-16 bg-neutral-700"></span>
      <span className="px-4 text-neutral-400">
        {finished ? "The End" : loading ? "Loading" : "Waiting"}
      </span>
      <span className="inline-block h-[1.5px] w-16 bg-neutral-700"></span>
    </div>
  );
}
