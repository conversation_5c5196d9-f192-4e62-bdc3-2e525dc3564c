import clsx from "clsx";
import { Dialog } from "radix-ui";
import {
  useEffect,
  useRef,
  useState,
  type PropsWithChildren,
} from "preact/compat";

interface Props {
  open?: boolean;
  onOpenChange?: (val: boolean) => void;
  onAfterOpen?: () => void;
  onAfterClose?: () => void;
  onBeforeClose?: () => void;
  onPointerDownOutside?: (event: Event) => void;
}
export function Drawer({ children, ...props }: PropsWithChildren<Props>) {
  const [isMount, setIsMount] = useState(props.open);
  const [state, setState] = useState<
    "opening" | "opened" | "closing" | "closed"
  >();
  const preState = useRef(state);

  useEffect(() => {
    if (props.open) {
      setState("opening");
    } else {
      setState("closing");
    }
  }, [props.open]);

  useEffect(() => {
    if (preState.current === state) return;

    // console.log(preState.current, "->", state);

    switch (state) {
      case "opening":
        setIsMount(true);
        break;
      case "closed":
        setIsMount(false);
        props.onAfterClose?.();
        break;
      case "opened":
        props.onAfterOpen?.();
        break;
      case "closing":
        props.onBeforeClose?.();
        break;
    }
    preState.current = state;
  }, [state, props.onAfterClose, props.onAfterOpen, props.onBeforeClose]);

  return (
    <Dialog.Root open={isMount} onOpenChange={props.onOpenChange} modal={false}>
      <Dialog.Portal>
        <Dialog.Content
          onPointerDownOutside={props.onPointerDownOutside}
          onAnimationEnd={(event) => {
            if (event.animationName === "slide-in") {
              setState("opened");
            }
            if (event.animationName === "slide-out") {
              setState("closed");
            }
          }}
          className={clsx(
            "fixed right-0 top-0 flex h-full w-screen transform bg-zinc-800 shadow-lg transition-transform duration-300 lg:w-[30rem]",
            (state === undefined || state === "closed") && "hidden",
            state === "opening" && "animate-slide-in",
            state === "closing" && "animate-slide-out"
          )}
        >
          <Dialog.Title></Dialog.Title>
          {children}
        </Dialog.Content>
      </Dialog.Portal>
    </Dialog.Root>
  );
}
