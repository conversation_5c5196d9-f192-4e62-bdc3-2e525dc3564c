// @ts-ignore - api-client has type conflicts but is generated code
import {
  Configuration,
  Default<PERSON>pi,
  RestApi,
  type RestTopicsTopicIdWallpapersGetRequest,
} from "../api-client";
import { TOPICS_PRE_PAGE, SizeEnum } from "./common";
import { processImageURL } from "./helper";
import pMap from "p-map";

const configuration = new Configuration({
  basePath: import.meta.env.BUILDTIME_API_BASE_URL,
});

export const restApi = new RestApi(configuration);

/**
 * 获取专题壁纸列表，预处理图片。
 */
async function restTopicsTopicIdWallpapersGet(
  requestParameters: RestTopicsTopicIdWallpapersGetRequest
) {
  const data = await restApi.restTopicsTopicIdWallpapersGet(requestParameters);
  for (const wallpaper of data.results) {
    for (const key in wallpaper.images) {
      wallpaper.images[key] = await processImageURL(wallpaper.images[key]);
    }
  }
  return data;
}

const TOPIC_WALLPAPER_COUNT = 100; // TODO: 目前假设专题壁纸都不超过100张，能够全部取出。如果超出了，后期需做专题页。

/**
 * 获取所有专题的壁纸，用于构建相似图片
 */
export async function* fetchAllTopicWallpapers() {
  while (true) {
    const topics = await restApi.restClientsClientIdClienttopicsGet({
      clientId: import.meta.env.CLIENT_ID,
      pageSize: 100,
    });
    for (const topic of topics.results) {
      const wallpapers = await restApi.restTopicsTopicIdWallpapersGet({
        topicId: topic.topicId,
        pageSize: TOPIC_WALLPAPER_COUNT,
      });
      for (const wallpaper of wallpapers.results) {
        yield wallpaper;
      }
    }
    if (topics.page * topics.pageSize >= topics.total) {
      break;
    }
  }
}

export interface FetchTopicListResult {
  results: Topic[];
  next?: number; // 下一页, 为 undefined 表示没有下一页
}
/**
 * Topic 列表聚合接口
 */
export async function fetchTopicList(
  page: number
): Promise<FetchTopicListResult> {
  const res = await restApi.restClientsClientIdClienttopicsGet({
    clientId: import.meta.env.CLIENT_ID,
    page: page,
    pageSize: TOPICS_PRE_PAGE, // 设置每页 X 条数据
  });

  const list = res.results;
  const detail = await pMap(list, async (i) => {
    return pMap(
      (
        await restTopicsTopicIdWallpapersGet({
          topicId: i.topicId,
          size: [SizeEnum.Middle],
          pageSize: TOPIC_WALLPAPER_COUNT,
        })
      ).results,
      async (i) => {
        const uploader = (
          await restApi.restWallpapersWallpaperIdUploadersGet({
            wallpaperId: i.id,
            pageSize: 1,
          })
        ).results[0];

        return {
          id: i.id,
          height: i.height,
          width: i.width,
          imageUrl: import.meta.env.DEV
            ? i.images._default
            : i.images[SizeEnum.Middle],
          uploader: uploader?.name,
          md5: i.contentMd5,
        } satisfies Wallpaper;
      }
    );
  });

  return {
    results: list.map((i, idx) => {
      return {
        title: i.title,
        wallpapers: detail[idx],
      } satisfies Topic;
    }),
    next: res.page * res.pageSize < res.total ? page + 1 : undefined,
  };
}
