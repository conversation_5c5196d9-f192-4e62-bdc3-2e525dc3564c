import { Default<PERSON>pi, type ImageKeyGetRequest } from "../api-client";
import { Configuration } from "../api-client/runtime";
import mime from "mime";

const configuration = new Configuration({
  basePath: "https://wallnest.oneproject.dev",
});
export const publicApi = new DefaultApi(configuration);

export async function downloadImage(requestParameters: ImageKeyGetRequest) {
  const res = (await publicApi.imageKeyGetRaw(requestParameters)).raw;
  if (!res.ok) return;
  const a = document.createElement("a");
  a.href = URL.createObjectURL(await res.blob());
  const filename = `${btoa(requestParameters.key)
    .replace(/=+$/, "")
    .slice(0, 12)}.${
    mime.getExtension(res.headers.get("content-type") || "image/jpeg") || "jpg"
  }`;
  a.download = filename;
  a.click();
}
