import { useEffect, useRef, useState } from "preact/hooks";
import { GalleryImage } from "../components/GalleryImage";
import { WallpaperDetail } from "./WallpaperDetail";
import type { FetchTopicListResult } from "../api-buildtime";
import { LoadMore } from "../components/LoadMore";
import { Drawer } from "src/components/Drawer";

export interface GalleryProps {
  initialValue: Topic[];
  next?: number;
}

export function GalleryView(props: GalleryProps) {
  const [selectedWallpaper, setSelectedWallpaper] = useState<Wallpaper>();

  const [topics, setTopics] = useState(() => [...props.initialValue]);

  const [moreLoading, setMoreLoading] = useState(false);
  const [moreFinished, setMoreFinished] = useState(props.next === undefined);
  const nextRef = useRef(props.next);

  const [drawerOpen, setDrawerOpen] = useState(false);
  const [showDrawerPlaceholder, setShowDrawerPlaceholder] = useState(false);

  return (
    <div className="flex w-screen">
      <div className="min-w-0 grow space-y-6">
        {topics.map((item) => (
          <div>
            <h6 className={"pl-6 text-2xl font-bold md:pl-12"}>{item.title}</h6>
            <div class="no-scrollbar w-full overflow-x-scroll py-4">
              <div className="flex gap-6 px-6 md:px-12">
                {item.wallpapers.map((wallpaper) => (
                  <div
                    className="skrink-0 cursor-pointer"
                    onClick={() => {
                      setSelectedWallpaper(wallpaper);
                      setDrawerOpen(true);
                    }}
                    data-disabled-close-drawer
                  >
                    <GalleryImage
                      focus={selectedWallpaper === wallpaper}
                      imageSrc={wallpaper.imageUrl}
                      imageSize={{
                        width: wallpaper.width,
                        height: wallpaper.height,
                      }}
                    />
                  </div>
                ))}
              </div>
            </div>
          </div>
        ))}
        <div className={"mb-4 text-center"}>
          <LoadMore
            loading={moreLoading}
            finished={moreFinished}
            onLoad={() => {
              if (nextRef.current === undefined) return;

              setMoreLoading(true);
              fetch(`/api/topics/${nextRef.current satisfies number}.json`)
                .then(async (resp) => {
                  if (resp.ok) {
                    const data = (await resp.json()) as FetchTopicListResult;
                    if (data.next === undefined) {
                      setMoreFinished(true);
                    } else {
                      nextRef.current = data.next;
                    }
                    setTopics((topics) => [...topics, ...data.results]);
                  }
                })
                .finally(() => {
                  setMoreLoading(false);
                });
            }}
          />
        </div>
      </div>

      {selectedWallpaper && (
        <>
          {showDrawerPlaceholder && (
            <div className="h-full w-[30rem] shrink-0">{/* 空间占位 */}</div>
          )}
          <Drawer
            open={drawerOpen}
            onOpenChange={setDrawerOpen}
            onBeforeClose={() => {
              setShowDrawerPlaceholder(false);
            }}
            onAfterOpen={() => {
              setShowDrawerPlaceholder(true);
            }}
            onAfterClose={() => {
              setSelectedWallpaper(undefined);
            }}
            onPointerDownOutside={(event) => {
              if (event.target instanceof HTMLElement) {
                if (event.target.closest("[data-disabled-close-drawer]")) {
                  event.preventDefault();
                }
              }
            }}
          >
            <WallpaperDetail
              wallpaper={selectedWallpaper}
              onClickClose={() => {
                setDrawerOpen(false);
              }}
            />
          </Drawer>
        </>
      )}
    </div>
  );
}
