import { useEffect, useRef, useState } from "preact/hooks";
import { TextButton } from "../components/Button";
import { ChevronLeft, ChevronRight, CloseIcon } from "../components/icons";
import { SizeEnum } from "../common";
import { downloadImage } from "../api-runtime";
import { IMac, Iphone13ProMax } from "../components/devices/index";

interface Props {
  wallpaper: Wallpaper;
  onClickChevronLeft?: () => void;
  onClickChevronRight?: () => void;
  onClickClose: () => void;
}

// Declare the turnstile property on the Window interface
declare global {
  interface Window {
    turnstile: any | undefined;
  }
}

export function WallpaperDetail(props: Props) {
  const [btnText, setBtnText] = useState<string>(); // 非 undfined 状态也用于 loading

  const [similarWallpapers, setSimilarWallpapers] = useState<Wallpaper[]>();

  useEffect(() => {
    setDisplayedWallpaper(props.wallpaper);
    fetch(
      `/api/similar/${import.meta.env.DEV ? "0" : props.wallpaper.id}.json`
    ).then(async (resp) => {
      if (resp.ok) {
        const data = await resp.json();
        setSimilarWallpapers(data);
      }
    });
  }, [props.wallpaper]);

  const [displayedWallpaper, setDisplayedWallpaper] = useState(props.wallpaper);

  const scrollContainerRef = useRef<HTMLDivElement>(null);
  useEffect(() => {
    scrollContainerRef.current?.scrollTo({
      top: 0,
    });
  }, [displayedWallpaper]);
  return (
    <div
      className="size-full overflow-y-scroll overscroll-contain"
      ref={scrollContainerRef}
    >
      {/* header */}
      <div className="flex space-x-5 px-3 py-3 text-neutral-400 *:cursor-pointer">
        <span className="ml-auto"></span> {/* 占位 */}
        {props.onClickChevronLeft && (
          <button onClick={props.onClickChevronLeft}>
            <ChevronLeft />
          </button>
        )}
        {props.onClickChevronRight && (
          <button onClick={props.onClickChevronRight}>
            <ChevronRight />
          </button>
        )}
        <button onClick={props.onClickClose}>
          <CloseIcon />
        </button>
      </div>

      {/* display image */}
      <div>
        <>
          {/* 图片展示区域 */}
          <div className="relative flex h-[30rem] items-center justify-center overflow-hidden">
            {/* 模糊背景 */}
            <div
              className="absolute left-0 top-0 h-full w-full scale-[120%]"
              style={{
                backgroundImage: `url(${displayedWallpaper.imageUrl})`,
                backgroundPosition: "center",
                backgroundSize: "cover",
                filter: "blur(15px)",
              }}
            ></div>

            {/* 设备 */}
            {displayedWallpaper.width > displayedWallpaper.height ? (
              <IMac backgroundImage={displayedWallpaper.imageUrl} />
            ) : (
              <Iphone13ProMax backgroundImage={displayedWallpaper.imageUrl} />
            )}
          </div>

          {displayedWallpaper.uploader && (
            <div>
              <p className="px-2 py-2 text-sm text-neutral-400">
                Uploaded by:{" "}
                <span className="underline">{displayedWallpaper.uploader}</span>
              </p>
            </div>
          )}

          {/* 下载按钮 */}
          <div className="flex flex-col items-center px-2 py-4">
            <div id="cf-turnstile" data-theme="dark"></div>
            <div className={"min-w-[50%]"}>
              <TextButton
                loading={btnText !== undefined}
                text={btnText || "Download HD Image"}
                onClick={() => {
                  setBtnText("Checking");

                  const turnstileEl = document.getElementById("cf-turnstile")!;
                  // 清除子元素
                  while (turnstileEl.firstChild) {
                    turnstileEl.removeChild(turnstileEl.firstChild);
                  }
                  window.turnstile.render(turnstileEl, {
                    sitekey: import.meta.env.PUBLIC_CF_TURNSTILE_SITEKEY,
                    callback: function (token: string) {
                      setBtnText("Downloading");
                      downloadImage({
                        key: displayedWallpaper.md5,
                        cFTurnstileResponse: token,
                      }).finally(() => {
                        setBtnText(undefined);
                      });
                    },
                  });
                }}
              ></TextButton>
            </div>
          </div>
        </>
      </div>

      {/* related wallpapers */}
      {!!similarWallpapers?.length && (
        <div className="border-t border-neutral-700">
          <ul className="grid grid-cols-2 gap-4 p-4 sm:grid-cols-3">
            {similarWallpapers.map((wallpaper) => {
              return (
                <li
                  key={wallpaper.md5}
                  className="h-36 cursor-pointer overflow-hidden rounded-md"
                  onClick={() => {
                    setDisplayedWallpaper(wallpaper);
                  }}
                >
                  <img
                    className="h-full w-full object-cover"
                    src={wallpaper.imageUrl}
                    alt=""
                    loading="lazy"
                  />
                </li>
              );
            })}
          </ul>
        </div>
      )}
    </div>
  );
}
