---
import { fetchTopicList } from "../api-buildtime";
import { GalleryView } from "../views/GalleryView";
import Layout from "../layouts/Layout.astro";
import Logo from "../components/Logo.astro";
// 服务端生成第一页，其它页从 API 获取
const res = await fetchTopicList(1);
---

<Layout>
  <header class="py-8 px-12 flex justify-center md:justify-start">
    <Logo />
  </header>
  <GalleryView client:load initialValue={res.results} next={res.next} />
</Layout>
