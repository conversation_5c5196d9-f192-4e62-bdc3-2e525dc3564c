/* tslint:disable */
/* eslint-disable */
/**
 * Wallpapers API
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 0.1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { RestClientsClientIdClienttopicsGet200ResponseResultsInnerTopic } from './RestClientsClientIdClienttopicsGet200ResponseResultsInnerTopic';
import {
    RestClientsClientIdClienttopicsGet200ResponseResultsInnerTopicFromJSON,
    RestClientsClientIdClienttopicsGet200ResponseResultsInnerTopicFromJSONTyped,
    RestClientsClientIdClienttopicsGet200ResponseResultsInnerTopicToJSON,
    RestClientsClientIdClienttopicsGet200ResponseResultsInnerTopicToJSONTyped,
} from './RestClientsClientIdClienttopicsGet200ResponseResultsInnerTopic';

/**
 * 
 * @export
 * @interface RestClientsClientIdClienttopicsGet200ResponseResultsInner
 */
export interface RestClientsClientIdClienttopicsGet200ResponseResultsInner {
    /**
     * 
     * @type {number}
     * @memberof RestClientsClientIdClienttopicsGet200ResponseResultsInner
     */
    id: number;
    /**
     * 
     * @type {string}
     * @memberof RestClientsClientIdClienttopicsGet200ResponseResultsInner
     */
    title: string;
    /**
     * 
     * @type {number}
     * @memberof RestClientsClientIdClienttopicsGet200ResponseResultsInner
     */
    topicId: number;
    /**
     * 
     * @type {RestClientsClientIdClienttopicsGet200ResponseResultsInnerTopic}
     * @memberof RestClientsClientIdClienttopicsGet200ResponseResultsInner
     */
    topic?: RestClientsClientIdClienttopicsGet200ResponseResultsInnerTopic;
    /**
     * 
     * @type {string}
     * @memberof RestClientsClientIdClienttopicsGet200ResponseResultsInner
     */
    clientId: string;
}

/**
 * Check if a given object implements the RestClientsClientIdClienttopicsGet200ResponseResultsInner interface.
 */
export function instanceOfRestClientsClientIdClienttopicsGet200ResponseResultsInner(value: object): value is RestClientsClientIdClienttopicsGet200ResponseResultsInner {
    if (!('id' in value) || value['id'] === undefined) return false;
    if (!('title' in value) || value['title'] === undefined) return false;
    if (!('topicId' in value) || value['topicId'] === undefined) return false;
    if (!('clientId' in value) || value['clientId'] === undefined) return false;
    return true;
}

export function RestClientsClientIdClienttopicsGet200ResponseResultsInnerFromJSON(json: any): RestClientsClientIdClienttopicsGet200ResponseResultsInner {
    return RestClientsClientIdClienttopicsGet200ResponseResultsInnerFromJSONTyped(json, false);
}

export function RestClientsClientIdClienttopicsGet200ResponseResultsInnerFromJSONTyped(json: any, ignoreDiscriminator: boolean): RestClientsClientIdClienttopicsGet200ResponseResultsInner {
    if (json == null) {
        return json;
    }
    return {
        
        'id': json['id'],
        'title': json['title'],
        'topicId': json['topic_id'],
        'topic': json['topic'] == null ? undefined : RestClientsClientIdClienttopicsGet200ResponseResultsInnerTopicFromJSON(json['topic']),
        'clientId': json['client_id'],
    };
}

export function RestClientsClientIdClienttopicsGet200ResponseResultsInnerToJSON(json: any): RestClientsClientIdClienttopicsGet200ResponseResultsInner {
    return RestClientsClientIdClienttopicsGet200ResponseResultsInnerToJSONTyped(json, false);
}

export function RestClientsClientIdClienttopicsGet200ResponseResultsInnerToJSONTyped(value?: RestClientsClientIdClienttopicsGet200ResponseResultsInner | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'id': value['id'],
        'title': value['title'],
        'topic_id': value['topicId'],
        'topic': RestClientsClientIdClienttopicsGet200ResponseResultsInnerTopicToJSON(value['topic']),
        'client_id': value['clientId'],
    };
}

