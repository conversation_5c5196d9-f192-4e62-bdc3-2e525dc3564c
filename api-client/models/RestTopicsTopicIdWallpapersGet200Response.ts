/* tslint:disable */
/* eslint-disable */
/**
 * Wallpapers API
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 0.1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { RestWallpapersWallpaperIdSimilarGet200ResponseResultsInnerWallpaper } from './RestWallpapersWallpaperIdSimilarGet200ResponseResultsInnerWallpaper';
import {
    RestWallpapersWallpaperIdSimilarGet200ResponseResultsInnerWallpaperFromJSON,
    RestWallpapersWallpaperIdSimilarGet200ResponseResultsInnerWallpaperFromJSONTyped,
    RestWallpapersWallpaperIdSimilarGet200ResponseResultsInnerWallpaperToJSON,
    RestWallpapersWallpaperIdSimilarGet200ResponseResultsInnerWallpaperToJSONTyped,
} from './RestWallpapersWallpaperIdSimilarGet200ResponseResultsInnerWallpaper';

/**
 * 
 * @export
 * @interface RestTopicsTopicIdWallpapersGet200Response
 */
export interface RestTopicsTopicIdWallpapersGet200Response {
    /**
     * 
     * @type {Array<RestWallpapersWallpaperIdSimilarGet200ResponseResultsInnerWallpaper>}
     * @memberof RestTopicsTopicIdWallpapersGet200Response
     */
    results: Array<RestWallpapersWallpaperIdSimilarGet200ResponseResultsInnerWallpaper>;
    /**
     * 
     * @type {number}
     * @memberof RestTopicsTopicIdWallpapersGet200Response
     */
    page: number;
    /**
     * 
     * @type {number}
     * @memberof RestTopicsTopicIdWallpapersGet200Response
     */
    pageSize: number;
    /**
     * 
     * @type {number}
     * @memberof RestTopicsTopicIdWallpapersGet200Response
     */
    total: number;
}

/**
 * Check if a given object implements the RestTopicsTopicIdWallpapersGet200Response interface.
 */
export function instanceOfRestTopicsTopicIdWallpapersGet200Response(value: object): value is RestTopicsTopicIdWallpapersGet200Response {
    if (!('results' in value) || value['results'] === undefined) return false;
    if (!('page' in value) || value['page'] === undefined) return false;
    if (!('pageSize' in value) || value['pageSize'] === undefined) return false;
    if (!('total' in value) || value['total'] === undefined) return false;
    return true;
}

export function RestTopicsTopicIdWallpapersGet200ResponseFromJSON(json: any): RestTopicsTopicIdWallpapersGet200Response {
    return RestTopicsTopicIdWallpapersGet200ResponseFromJSONTyped(json, false);
}

export function RestTopicsTopicIdWallpapersGet200ResponseFromJSONTyped(json: any, ignoreDiscriminator: boolean): RestTopicsTopicIdWallpapersGet200Response {
    if (json == null) {
        return json;
    }
    return {
        
        'results': ((json['results'] as Array<any>).map(RestWallpapersWallpaperIdSimilarGet200ResponseResultsInnerWallpaperFromJSON)),
        'page': json['page'],
        'pageSize': json['page_size'],
        'total': json['total'],
    };
}

export function RestTopicsTopicIdWallpapersGet200ResponseToJSON(json: any): RestTopicsTopicIdWallpapersGet200Response {
    return RestTopicsTopicIdWallpapersGet200ResponseToJSONTyped(json, false);
}

export function RestTopicsTopicIdWallpapersGet200ResponseToJSONTyped(value?: RestTopicsTopicIdWallpapersGet200Response | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'results': ((value['results'] as Array<any>).map(RestWallpapersWallpaperIdSimilarGet200ResponseResultsInnerWallpaperToJSON)),
        'page': value['page'],
        'page_size': value['pageSize'],
        'total': value['total'],
    };
}

