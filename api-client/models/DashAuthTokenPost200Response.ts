/* tslint:disable */
/* eslint-disable */
/**
 * Wallpapers API
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 0.1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface DashAuthTokenPost200Response
 */
export interface DashAuthTokenPost200Response {
    /**
     * 
     * @type {string}
     * @memberof DashAuthTokenPost200Response
     */
    accessToken: string;
}

/**
 * Check if a given object implements the DashAuthTokenPost200Response interface.
 */
export function instanceOfDashAuthTokenPost200Response(value: object): value is DashAuthTokenPost200Response {
    if (!('accessToken' in value) || value['accessToken'] === undefined) return false;
    return true;
}

export function DashAuthTokenPost200ResponseFromJSON(json: any): DashAuthTokenPost200Response {
    return DashAuthTokenPost200ResponseFromJSONTyped(json, false);
}

export function DashAuthTokenPost200ResponseFromJSONTyped(json: any, ignoreDiscriminator: boolean): DashAuthTokenPost200Response {
    if (json == null) {
        return json;
    }
    return {
        
        'accessToken': json['access_token'],
    };
}

export function DashAuthTokenPost200ResponseToJSON(json: any): DashAuthTokenPost200Response {
    return DashAuthTokenPost200ResponseToJSONTyped(json, false);
}

export function DashAuthTokenPost200ResponseToJSONTyped(value?: DashAuthTokenPost200Response | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'access_token': value['accessToken'],
    };
}

