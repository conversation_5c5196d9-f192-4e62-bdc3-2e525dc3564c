/* tslint:disable */
/* eslint-disable */
/**
 * Wallpapers API
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 0.1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface TopicsGet200ResponseTopicsInner
 */
export interface TopicsGet200ResponseTopicsInner {
    /**
     * 
     * @type {number}
     * @memberof TopicsGet200ResponseTopicsInner
     */
    id: number;
    /**
     * 
     * @type {string}
     * @memberof TopicsGet200ResponseTopicsInner
     */
    title: string;
}

/**
 * Check if a given object implements the TopicsGet200ResponseTopicsInner interface.
 */
export function instanceOfTopicsGet200ResponseTopicsInner(value: object): value is TopicsGet200ResponseTopicsInner {
    if (!('id' in value) || value['id'] === undefined) return false;
    if (!('title' in value) || value['title'] === undefined) return false;
    return true;
}

export function TopicsGet200ResponseTopicsInnerFromJSON(json: any): TopicsGet200ResponseTopicsInner {
    return TopicsGet200ResponseTopicsInnerFromJSONTyped(json, false);
}

export function TopicsGet200ResponseTopicsInnerFromJSONTyped(json: any, ignoreDiscriminator: boolean): TopicsGet200ResponseTopicsInner {
    if (json == null) {
        return json;
    }
    return {
        
        'id': json['id'],
        'title': json['title'],
    };
}

export function TopicsGet200ResponseTopicsInnerToJSON(json: any): TopicsGet200ResponseTopicsInner {
    return TopicsGet200ResponseTopicsInnerToJSONTyped(json, false);
}

export function TopicsGet200ResponseTopicsInnerToJSONTyped(value?: TopicsGet200ResponseTopicsInner | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'id': value['id'],
        'title': value['title'],
    };
}

