/* tslint:disable */
/* eslint-disable */
/**
 * Wallpapers API
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 0.1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface CategoriesColorsGet200ResponseColorsInner
 */
export interface CategoriesColorsGet200ResponseColorsInner {
    /**
     * 
     * @type {string}
     * @memberof CategoriesColorsGet200ResponseColorsInner
     */
    value: string;
    /**
     * 
     * @type {string}
     * @memberof CategoriesColorsGet200ResponseColorsInner
     */
    rGB: string;
}

/**
 * Check if a given object implements the CategoriesColorsGet200ResponseColorsInner interface.
 */
export function instanceOfCategoriesColorsGet200ResponseColorsInner(value: object): value is CategoriesColorsGet200ResponseColorsInner {
    if (!('value' in value) || value['value'] === undefined) return false;
    if (!('rGB' in value) || value['rGB'] === undefined) return false;
    return true;
}

export function CategoriesColorsGet200ResponseColorsInnerFromJSON(json: any): CategoriesColorsGet200ResponseColorsInner {
    return CategoriesColorsGet200ResponseColorsInnerFromJSONTyped(json, false);
}

export function CategoriesColorsGet200ResponseColorsInnerFromJSONTyped(json: any, ignoreDiscriminator: boolean): CategoriesColorsGet200ResponseColorsInner {
    if (json == null) {
        return json;
    }
    return {
        
        'value': json['value'],
        'rGB': json['RGB'],
    };
}

export function CategoriesColorsGet200ResponseColorsInnerToJSON(json: any): CategoriesColorsGet200ResponseColorsInner {
    return CategoriesColorsGet200ResponseColorsInnerToJSONTyped(json, false);
}

export function CategoriesColorsGet200ResponseColorsInnerToJSONTyped(value?: CategoriesColorsGet200ResponseColorsInner | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'value': value['value'],
        'RGB': value['rGB'],
    };
}

