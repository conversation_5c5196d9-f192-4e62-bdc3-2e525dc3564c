/* tslint:disable */
/* eslint-disable */
/**
 * Wallpapers API
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 0.1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { WallpapersGet200ResponseWallpapersInner } from './WallpapersGet200ResponseWallpapersInner';
import {
    WallpapersGet200ResponseWallpapersInnerFromJSON,
    WallpapersGet200ResponseWallpapersInnerFromJSONTyped,
    WallpapersGet200ResponseWallpapersInnerToJSON,
    WallpapersGet200ResponseWallpapersInnerToJSONTyped,
} from './WallpapersGet200ResponseWallpapersInner';

/**
 * 
 * @export
 * @interface WallpapersKeyRelatedGet200Response
 */
export interface WallpapersKeyRelatedGet200Response {
    /**
     * 
     * @type {Array<WallpapersGet200ResponseWallpapersInner>}
     * @memberof WallpapersKeyRelatedGet200Response
     */
    related: Array<WallpapersGet200ResponseWallpapersInner>;
}

/**
 * Check if a given object implements the WallpapersKeyRelatedGet200Response interface.
 */
export function instanceOfWallpapersKeyRelatedGet200Response(value: object): value is WallpapersKeyRelatedGet200Response {
    if (!('related' in value) || value['related'] === undefined) return false;
    return true;
}

export function WallpapersKeyRelatedGet200ResponseFromJSON(json: any): WallpapersKeyRelatedGet200Response {
    return WallpapersKeyRelatedGet200ResponseFromJSONTyped(json, false);
}

export function WallpapersKeyRelatedGet200ResponseFromJSONTyped(json: any, ignoreDiscriminator: boolean): WallpapersKeyRelatedGet200Response {
    if (json == null) {
        return json;
    }
    return {
        
        'related': ((json['related'] as Array<any>).map(WallpapersGet200ResponseWallpapersInnerFromJSON)),
    };
}

export function WallpapersKeyRelatedGet200ResponseToJSON(json: any): WallpapersKeyRelatedGet200Response {
    return WallpapersKeyRelatedGet200ResponseToJSONTyped(json, false);
}

export function WallpapersKeyRelatedGet200ResponseToJSONTyped(value?: WallpapersKeyRelatedGet200Response | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'related': ((value['related'] as Array<any>).map(WallpapersGet200ResponseWallpapersInnerToJSON)),
    };
}

