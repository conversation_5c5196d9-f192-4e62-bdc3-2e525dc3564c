/* tslint:disable */
/* eslint-disable */
/**
 * Wallpapers API
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 0.1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { RestWallpapersGet200ResponseResultsInner } from './RestWallpapersGet200ResponseResultsInner';
import {
    RestWallpapersGet200ResponseResultsInnerFromJSON,
    RestWallpapersGet200ResponseResultsInnerFromJSONTyped,
    RestWallpapersGet200ResponseResultsInnerToJSON,
    RestWallpapersGet200ResponseResultsInnerToJSONTyped,
} from './RestWallpapersGet200ResponseResultsInner';

/**
 * 
 * @export
 * @interface RestWallpapersGet200Response
 */
export interface RestWallpapersGet200Response {
    /**
     * 
     * @type {Array<RestWallpapersGet200ResponseResultsInner>}
     * @memberof RestWallpapersGet200Response
     */
    results: Array<RestWallpapersGet200ResponseResultsInner>;
    /**
     * 
     * @type {number}
     * @memberof RestWallpapersGet200Response
     */
    page: number;
    /**
     * 
     * @type {number}
     * @memberof RestWallpapersGet200Response
     */
    pageSize: number;
    /**
     * 
     * @type {number}
     * @memberof RestWallpapersGet200Response
     */
    total: number;
}

/**
 * Check if a given object implements the RestWallpapersGet200Response interface.
 */
export function instanceOfRestWallpapersGet200Response(value: object): value is RestWallpapersGet200Response {
    if (!('results' in value) || value['results'] === undefined) return false;
    if (!('page' in value) || value['page'] === undefined) return false;
    if (!('pageSize' in value) || value['pageSize'] === undefined) return false;
    if (!('total' in value) || value['total'] === undefined) return false;
    return true;
}

export function RestWallpapersGet200ResponseFromJSON(json: any): RestWallpapersGet200Response {
    return RestWallpapersGet200ResponseFromJSONTyped(json, false);
}

export function RestWallpapersGet200ResponseFromJSONTyped(json: any, ignoreDiscriminator: boolean): RestWallpapersGet200Response {
    if (json == null) {
        return json;
    }
    return {
        
        'results': ((json['results'] as Array<any>).map(RestWallpapersGet200ResponseResultsInnerFromJSON)),
        'page': json['page'],
        'pageSize': json['page_size'],
        'total': json['total'],
    };
}

export function RestWallpapersGet200ResponseToJSON(json: any): RestWallpapersGet200Response {
    return RestWallpapersGet200ResponseToJSONTyped(json, false);
}

export function RestWallpapersGet200ResponseToJSONTyped(value?: RestWallpapersGet200Response | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'results': ((value['results'] as Array<any>).map(RestWallpapersGet200ResponseResultsInnerToJSON)),
        'page': value['page'],
        'page_size': value['pageSize'],
        'total': value['total'],
    };
}

