/* tslint:disable */
/* eslint-disable */
/**
 * Wallpapers API
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 0.1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { DashTopicsTopicIdWallpapersGet200ResponseResultsInner } from './DashTopicsTopicIdWallpapersGet200ResponseResultsInner';
import {
    DashTopicsTopicIdWallpapersGet200ResponseResultsInnerFromJSON,
    DashTopicsTopicIdWallpapersGet200ResponseResultsInnerFromJSONTyped,
    DashTopicsTopicIdWallpapersGet200ResponseResultsInnerToJSON,
    DashTopicsTopicIdWallpapersGet200ResponseResultsInnerToJSONTyped,
} from './DashTopicsTopicIdWallpapersGet200ResponseResultsInner';

/**
 * 
 * @export
 * @interface DashTopicsTopicIdWallpapersGet200Response
 */
export interface DashTopicsTopicIdWallpapersGet200Response {
    /**
     * 
     * @type {Array<DashTopicsTopicIdWallpapersGet200ResponseResultsInner>}
     * @memberof DashTopicsTopicIdWallpapersGet200Response
     */
    results: Array<DashTopicsTopicIdWallpapersGet200ResponseResultsInner>;
    /**
     * 
     * @type {number}
     * @memberof DashTopicsTopicIdWallpapersGet200Response
     */
    page: number;
    /**
     * 
     * @type {number}
     * @memberof DashTopicsTopicIdWallpapersGet200Response
     */
    pageSize: number;
    /**
     * 
     * @type {number}
     * @memberof DashTopicsTopicIdWallpapersGet200Response
     */
    total: number;
}

/**
 * Check if a given object implements the DashTopicsTopicIdWallpapersGet200Response interface.
 */
export function instanceOfDashTopicsTopicIdWallpapersGet200Response(value: object): value is DashTopicsTopicIdWallpapersGet200Response {
    if (!('results' in value) || value['results'] === undefined) return false;
    if (!('page' in value) || value['page'] === undefined) return false;
    if (!('pageSize' in value) || value['pageSize'] === undefined) return false;
    if (!('total' in value) || value['total'] === undefined) return false;
    return true;
}

export function DashTopicsTopicIdWallpapersGet200ResponseFromJSON(json: any): DashTopicsTopicIdWallpapersGet200Response {
    return DashTopicsTopicIdWallpapersGet200ResponseFromJSONTyped(json, false);
}

export function DashTopicsTopicIdWallpapersGet200ResponseFromJSONTyped(json: any, ignoreDiscriminator: boolean): DashTopicsTopicIdWallpapersGet200Response {
    if (json == null) {
        return json;
    }
    return {
        
        'results': ((json['results'] as Array<any>).map(DashTopicsTopicIdWallpapersGet200ResponseResultsInnerFromJSON)),
        'page': json['page'],
        'pageSize': json['page_size'],
        'total': json['total'],
    };
}

export function DashTopicsTopicIdWallpapersGet200ResponseToJSON(json: any): DashTopicsTopicIdWallpapersGet200Response {
    return DashTopicsTopicIdWallpapersGet200ResponseToJSONTyped(json, false);
}

export function DashTopicsTopicIdWallpapersGet200ResponseToJSONTyped(value?: DashTopicsTopicIdWallpapersGet200Response | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'results': ((value['results'] as Array<any>).map(DashTopicsTopicIdWallpapersGet200ResponseResultsInnerToJSON)),
        'page': value['page'],
        'page_size': value['pageSize'],
        'total': value['total'],
    };
}

