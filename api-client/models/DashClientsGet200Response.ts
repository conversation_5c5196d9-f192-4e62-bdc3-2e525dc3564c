/* tslint:disable */
/* eslint-disable */
/**
 * Wallpapers API
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 0.1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { DashTopicsTopicIdClienttopicsGet200ResponseResultsInnerClient } from './DashTopicsTopicIdClienttopicsGet200ResponseResultsInnerClient';
import {
    DashTopicsTopicIdClienttopicsGet200ResponseResultsInnerClientFromJSON,
    DashTopicsTopicIdClienttopicsGet200ResponseResultsInnerClientFromJSONTyped,
    DashTopicsTopicIdClienttopicsGet200ResponseResultsInnerClientToJSON,
    DashTopicsTopicIdClienttopicsGet200ResponseResultsInnerClientToJSONTyped,
} from './DashTopicsTopicIdClienttopicsGet200ResponseResultsInnerClient';

/**
 * 
 * @export
 * @interface DashClientsGet200Response
 */
export interface DashClientsGet200Response {
    /**
     * 
     * @type {number}
     * @memberof DashClientsGet200Response
     */
    page?: number;
    /**
     * 
     * @type {number}
     * @memberof DashClientsGet200Response
     */
    pageSize?: number;
    /**
     * 
     * @type {number}
     * @memberof DashClientsGet200Response
     */
    total?: number;
    /**
     * 
     * @type {Array<DashTopicsTopicIdClienttopicsGet200ResponseResultsInnerClient>}
     * @memberof DashClientsGet200Response
     */
    results?: Array<DashTopicsTopicIdClienttopicsGet200ResponseResultsInnerClient>;
}

/**
 * Check if a given object implements the DashClientsGet200Response interface.
 */
export function instanceOfDashClientsGet200Response(value: object): value is DashClientsGet200Response {
    return true;
}

export function DashClientsGet200ResponseFromJSON(json: any): DashClientsGet200Response {
    return DashClientsGet200ResponseFromJSONTyped(json, false);
}

export function DashClientsGet200ResponseFromJSONTyped(json: any, ignoreDiscriminator: boolean): DashClientsGet200Response {
    if (json == null) {
        return json;
    }
    return {
        
        'page': json['page'] == null ? undefined : json['page'],
        'pageSize': json['page_size'] == null ? undefined : json['page_size'],
        'total': json['total'] == null ? undefined : json['total'],
        'results': json['results'] == null ? undefined : ((json['results'] as Array<any>).map(DashTopicsTopicIdClienttopicsGet200ResponseResultsInnerClientFromJSON)),
    };
}

export function DashClientsGet200ResponseToJSON(json: any): DashClientsGet200Response {
    return DashClientsGet200ResponseToJSONTyped(json, false);
}

export function DashClientsGet200ResponseToJSONTyped(value?: DashClientsGet200Response | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'page': value['page'],
        'page_size': value['pageSize'],
        'total': value['total'],
        'results': value['results'] == null ? undefined : ((value['results'] as Array<any>).map(DashTopicsTopicIdClienttopicsGet200ResponseResultsInnerClientToJSON)),
    };
}

