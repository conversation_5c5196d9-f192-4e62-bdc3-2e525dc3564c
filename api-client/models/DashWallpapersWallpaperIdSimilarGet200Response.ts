/* tslint:disable */
/* eslint-disable */
/**
 * Wallpapers API
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 0.1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { DashWallpapersWallpaperIdSimilarGet200ResponseResultsInner } from './DashWallpapersWallpaperIdSimilarGet200ResponseResultsInner';
import {
    DashWallpapersWallpaperIdSimilarGet200ResponseResultsInnerFromJSON,
    DashWallpapersWallpaperIdSimilarGet200ResponseResultsInnerFromJSONTyped,
    DashWallpapersWallpaperIdSimilarGet200ResponseResultsInnerToJSON,
    DashWallpapersWallpaperIdSimilarGet200ResponseResultsInnerToJSONTyped,
} from './DashWallpapersWallpaperIdSimilarGet200ResponseResultsInner';

/**
 * 
 * @export
 * @interface DashWallpapersWallpaperIdSimilarGet200Response
 */
export interface DashWallpapersWallpaperIdSimilarGet200Response {
    /**
     * 
     * @type {Array<DashWallpapersWallpaperIdSimilarGet200ResponseResultsInner>}
     * @memberof DashWallpapersWallpaperIdSimilarGet200Response
     */
    results: Array<DashWallpapersWallpaperIdSimilarGet200ResponseResultsInner>;
}

/**
 * Check if a given object implements the DashWallpapersWallpaperIdSimilarGet200Response interface.
 */
export function instanceOfDashWallpapersWallpaperIdSimilarGet200Response(value: object): value is DashWallpapersWallpaperIdSimilarGet200Response {
    if (!('results' in value) || value['results'] === undefined) return false;
    return true;
}

export function DashWallpapersWallpaperIdSimilarGet200ResponseFromJSON(json: any): DashWallpapersWallpaperIdSimilarGet200Response {
    return DashWallpapersWallpaperIdSimilarGet200ResponseFromJSONTyped(json, false);
}

export function DashWallpapersWallpaperIdSimilarGet200ResponseFromJSONTyped(json: any, ignoreDiscriminator: boolean): DashWallpapersWallpaperIdSimilarGet200Response {
    if (json == null) {
        return json;
    }
    return {
        
        'results': ((json['results'] as Array<any>).map(DashWallpapersWallpaperIdSimilarGet200ResponseResultsInnerFromJSON)),
    };
}

export function DashWallpapersWallpaperIdSimilarGet200ResponseToJSON(json: any): DashWallpapersWallpaperIdSimilarGet200Response {
    return DashWallpapersWallpaperIdSimilarGet200ResponseToJSONTyped(json, false);
}

export function DashWallpapersWallpaperIdSimilarGet200ResponseToJSONTyped(value?: DashWallpapersWallpaperIdSimilarGet200Response | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'results': ((value['results'] as Array<any>).map(DashWallpapersWallpaperIdSimilarGet200ResponseResultsInnerToJSON)),
    };
}

