/* tslint:disable */
/* eslint-disable */
/**
 * Wallpapers API
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 0.1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface DashTopicTopicIdPublishClientIdPostRequest
 */
export interface DashTopicTopicIdPublishClientIdPostRequest {
    /**
     * 
     * @type {string}
     * @memberof DashTopicTopicIdPublishClientIdPostRequest
     */
    title: string;
}

/**
 * Check if a given object implements the DashTopicTopicIdPublishClientIdPostRequest interface.
 */
export function instanceOfDashTopicTopicIdPublishClientIdPostRequest(value: object): value is DashTopicTopicIdPublishClientIdPostRequest {
    if (!('title' in value) || value['title'] === undefined) return false;
    return true;
}

export function DashTopicTopicIdPublishClientIdPostRequestFromJSON(json: any): DashTopicTopicIdPublishClientIdPostRequest {
    return DashTopicTopicIdPublishClientIdPostRequestFromJSONTyped(json, false);
}

export function DashTopicTopicIdPublishClientIdPostRequestFromJSONTyped(json: any, ignoreDiscriminator: boolean): DashTopicTopicIdPublishClientIdPostRequest {
    if (json == null) {
        return json;
    }
    return {
        
        'title': json['title'],
    };
}

export function DashTopicTopicIdPublishClientIdPostRequestToJSON(json: any): DashTopicTopicIdPublishClientIdPostRequest {
    return DashTopicTopicIdPublishClientIdPostRequestToJSONTyped(json, false);
}

export function DashTopicTopicIdPublishClientIdPostRequestToJSONTyped(value?: DashTopicTopicIdPublishClientIdPostRequest | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'title': value['title'],
    };
}

