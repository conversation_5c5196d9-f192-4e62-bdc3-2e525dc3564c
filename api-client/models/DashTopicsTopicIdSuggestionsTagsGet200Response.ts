/* tslint:disable */
/* eslint-disable */
/**
 * Wallpapers API
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 0.1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface DashTopicsTopicIdSuggestionsTagsGet200Response
 */
export interface DashTopicsTopicIdSuggestionsTagsGet200Response {
    /**
     * 
     * @type {Array<string>}
     * @memberof DashTopicsTopicIdSuggestionsTagsGet200Response
     */
    tags: Array<string>;
}

/**
 * Check if a given object implements the DashTopicsTopicIdSuggestionsTagsGet200Response interface.
 */
export function instanceOfDashTopicsTopicIdSuggestionsTagsGet200Response(value: object): value is DashTopicsTopicIdSuggestionsTagsGet200Response {
    if (!('tags' in value) || value['tags'] === undefined) return false;
    return true;
}

export function DashTopicsTopicIdSuggestionsTagsGet200ResponseFromJSON(json: any): DashTopicsTopicIdSuggestionsTagsGet200Response {
    return DashTopicsTopicIdSuggestionsTagsGet200ResponseFromJSONTyped(json, false);
}

export function DashTopicsTopicIdSuggestionsTagsGet200ResponseFromJSONTyped(json: any, ignoreDiscriminator: boolean): DashTopicsTopicIdSuggestionsTagsGet200Response {
    if (json == null) {
        return json;
    }
    return {
        
        'tags': json['tags'],
    };
}

export function DashTopicsTopicIdSuggestionsTagsGet200ResponseToJSON(json: any): DashTopicsTopicIdSuggestionsTagsGet200Response {
    return DashTopicsTopicIdSuggestionsTagsGet200ResponseToJSONTyped(json, false);
}

export function DashTopicsTopicIdSuggestionsTagsGet200ResponseToJSONTyped(value?: DashTopicsTopicIdSuggestionsTagsGet200Response | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'tags': value['tags'],
    };
}

