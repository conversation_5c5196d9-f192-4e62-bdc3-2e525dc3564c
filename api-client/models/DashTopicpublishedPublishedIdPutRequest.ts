/* tslint:disable */
/* eslint-disable */
/**
 * Wallpapers API
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 0.1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface DashTopicpublishedPublishedIdPutRequest
 */
export interface DashTopicpublishedPublishedIdPutRequest {
    /**
     * 
     * @type {string}
     * @memberof DashTopicpublishedPublishedIdPutRequest
     */
    title: string;
}

/**
 * Check if a given object implements the DashTopicpublishedPublishedIdPutRequest interface.
 */
export function instanceOfDashTopicpublishedPublishedIdPutRequest(value: object): value is DashTopicpublishedPublishedIdPutRequest {
    if (!('title' in value) || value['title'] === undefined) return false;
    return true;
}

export function DashTopicpublishedPublishedIdPutRequestFromJSON(json: any): DashTopicpublishedPublishedIdPutRequest {
    return DashTopicpublishedPublishedIdPutRequestFromJSONTyped(json, false);
}

export function DashTopicpublishedPublishedIdPutRequestFromJSONTyped(json: any, ignoreDiscriminator: boolean): DashTopicpublishedPublishedIdPutRequest {
    if (json == null) {
        return json;
    }
    return {
        
        'title': json['title'],
    };
}

export function DashTopicpublishedPublishedIdPutRequestToJSON(json: any): DashTopicpublishedPublishedIdPutRequest {
    return DashTopicpublishedPublishedIdPutRequestToJSONTyped(json, false);
}

export function DashTopicpublishedPublishedIdPutRequestToJSONTyped(value?: DashTopicpublishedPublishedIdPutRequest | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'title': value['title'],
    };
}

