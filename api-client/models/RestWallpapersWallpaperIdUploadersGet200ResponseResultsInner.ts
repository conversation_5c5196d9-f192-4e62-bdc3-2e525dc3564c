/* tslint:disable */
/* eslint-disable */
/**
 * Wallpapers API
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 0.1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface RestWallpapersWallpaperIdUploadersGet200ResponseResultsInner
 */
export interface RestWallpapersWallpaperIdUploadersGet200ResponseResultsInner {
    /**
     * 
     * @type {number}
     * @memberof RestWallpapersWallpaperIdUploadersGet200ResponseResultsInner
     */
    id: number;
    /**
     * 
     * @type {string}
     * @memberof RestWallpapersWallpaperIdUploadersGet200ResponseResultsInner
     */
    name: string;
}

/**
 * Check if a given object implements the RestWallpapersWallpaperIdUploadersGet200ResponseResultsInner interface.
 */
export function instanceOfRestWallpapersWallpaperIdUploadersGet200ResponseResultsInner(value: object): value is RestWallpapersWallpaperIdUploadersGet200ResponseResultsInner {
    if (!('id' in value) || value['id'] === undefined) return false;
    if (!('name' in value) || value['name'] === undefined) return false;
    return true;
}

export function RestWallpapersWallpaperIdUploadersGet200ResponseResultsInnerFromJSON(json: any): RestWallpapersWallpaperIdUploadersGet200ResponseResultsInner {
    return RestWallpapersWallpaperIdUploadersGet200ResponseResultsInnerFromJSONTyped(json, false);
}

export function RestWallpapersWallpaperIdUploadersGet200ResponseResultsInnerFromJSONTyped(json: any, ignoreDiscriminator: boolean): RestWallpapersWallpaperIdUploadersGet200ResponseResultsInner {
    if (json == null) {
        return json;
    }
    return {
        
        'id': json['id'],
        'name': json['name'],
    };
}

export function RestWallpapersWallpaperIdUploadersGet200ResponseResultsInnerToJSON(json: any): RestWallpapersWallpaperIdUploadersGet200ResponseResultsInner {
    return RestWallpapersWallpaperIdUploadersGet200ResponseResultsInnerToJSONTyped(json, false);
}

export function RestWallpapersWallpaperIdUploadersGet200ResponseResultsInnerToJSONTyped(value?: RestWallpapersWallpaperIdUploadersGet200ResponseResultsInner | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'id': value['id'],
        'name': value['name'],
    };
}

