/* tslint:disable */
/* eslint-disable */
/**
 * Wallpapers API
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 0.1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { RestWallpapersGet200ResponseResultsInner } from './RestWallpapersGet200ResponseResultsInner';
import {
    RestWallpapersGet200ResponseResultsInnerFromJSON,
    RestWallpapersGet200ResponseResultsInnerFromJSONTyped,
    RestWallpapersGet200ResponseResultsInnerToJSON,
    RestWallpapersGet200ResponseResultsInnerToJSONTyped,
} from './RestWallpapersGet200ResponseResultsInner';

/**
 * 
 * @export
 * @interface RestWallpapersWallpaperIdSimilarGet200ResponseResultsInner
 */
export interface RestWallpapersWallpaperIdSimilarGet200ResponseResultsInner {
    /**
     * 
     * @type {RestWallpapersGet200ResponseResultsInner}
     * @memberof RestWallpapersWallpaperIdSimilarGet200ResponseResultsInner
     */
    wallpaper: RestWallpapersGet200ResponseResultsInner;
    /**
     * 
     * @type {number}
     * @memberof RestWallpapersWallpaperIdSimilarGet200ResponseResultsInner
     */
    score: number;
}

/**
 * Check if a given object implements the RestWallpapersWallpaperIdSimilarGet200ResponseResultsInner interface.
 */
export function instanceOfRestWallpapersWallpaperIdSimilarGet200ResponseResultsInner(value: object): value is RestWallpapersWallpaperIdSimilarGet200ResponseResultsInner {
    if (!('wallpaper' in value) || value['wallpaper'] === undefined) return false;
    if (!('score' in value) || value['score'] === undefined) return false;
    return true;
}

export function RestWallpapersWallpaperIdSimilarGet200ResponseResultsInnerFromJSON(json: any): RestWallpapersWallpaperIdSimilarGet200ResponseResultsInner {
    return RestWallpapersWallpaperIdSimilarGet200ResponseResultsInnerFromJSONTyped(json, false);
}

export function RestWallpapersWallpaperIdSimilarGet200ResponseResultsInnerFromJSONTyped(json: any, ignoreDiscriminator: boolean): RestWallpapersWallpaperIdSimilarGet200ResponseResultsInner {
    if (json == null) {
        return json;
    }
    return {
        
        'wallpaper': RestWallpapersGet200ResponseResultsInnerFromJSON(json['wallpaper']),
        'score': json['score'],
    };
}

export function RestWallpapersWallpaperIdSimilarGet200ResponseResultsInnerToJSON(json: any): RestWallpapersWallpaperIdSimilarGet200ResponseResultsInner {
    return RestWallpapersWallpaperIdSimilarGet200ResponseResultsInnerToJSONTyped(json, false);
}

export function RestWallpapersWallpaperIdSimilarGet200ResponseResultsInnerToJSONTyped(value?: RestWallpapersWallpaperIdSimilarGet200ResponseResultsInner | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'wallpaper': RestWallpapersGet200ResponseResultsInnerToJSON(value['wallpaper']),
        'score': value['score'],
    };
}

