/* tslint:disable */
/* eslint-disable */
/**
 * Wallpapers API
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 0.1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { RestWallpapersWallpaperIdUploadersGet200ResponseResultsInner } from './RestWallpapersWallpaperIdUploadersGet200ResponseResultsInner';
import {
    RestWallpapersWallpaperIdUploadersGet200ResponseResultsInnerFromJSON,
    RestWallpapersWallpaperIdUploadersGet200ResponseResultsInnerFromJSONTyped,
    RestWallpapersWallpaperIdUploadersGet200ResponseResultsInnerToJSON,
    RestWallpapersWallpaperIdUploadersGet200ResponseResultsInnerToJSONTyped,
} from './RestWallpapersWallpaperIdUploadersGet200ResponseResultsInner';

/**
 * 
 * @export
 * @interface RestWallpapersWallpaperIdUploadersGet200Response
 */
export interface RestWallpapersWallpaperIdUploadersGet200Response {
    /**
     * 
     * @type {Array<RestWallpapersWallpaperIdUploadersGet200ResponseResultsInner>}
     * @memberof RestWallpapersWallpaperIdUploadersGet200Response
     */
    results: Array<RestWallpapersWallpaperIdUploadersGet200ResponseResultsInner>;
    /**
     * 
     * @type {number}
     * @memberof RestWallpapersWallpaperIdUploadersGet200Response
     */
    page: number;
    /**
     * 
     * @type {number}
     * @memberof RestWallpapersWallpaperIdUploadersGet200Response
     */
    pageSize: number;
    /**
     * 
     * @type {number}
     * @memberof RestWallpapersWallpaperIdUploadersGet200Response
     */
    total: number;
}

/**
 * Check if a given object implements the RestWallpapersWallpaperIdUploadersGet200Response interface.
 */
export function instanceOfRestWallpapersWallpaperIdUploadersGet200Response(value: object): value is RestWallpapersWallpaperIdUploadersGet200Response {
    if (!('results' in value) || value['results'] === undefined) return false;
    if (!('page' in value) || value['page'] === undefined) return false;
    if (!('pageSize' in value) || value['pageSize'] === undefined) return false;
    if (!('total' in value) || value['total'] === undefined) return false;
    return true;
}

export function RestWallpapersWallpaperIdUploadersGet200ResponseFromJSON(json: any): RestWallpapersWallpaperIdUploadersGet200Response {
    return RestWallpapersWallpaperIdUploadersGet200ResponseFromJSONTyped(json, false);
}

export function RestWallpapersWallpaperIdUploadersGet200ResponseFromJSONTyped(json: any, ignoreDiscriminator: boolean): RestWallpapersWallpaperIdUploadersGet200Response {
    if (json == null) {
        return json;
    }
    return {
        
        'results': ((json['results'] as Array<any>).map(RestWallpapersWallpaperIdUploadersGet200ResponseResultsInnerFromJSON)),
        'page': json['page'],
        'pageSize': json['page_size'],
        'total': json['total'],
    };
}

export function RestWallpapersWallpaperIdUploadersGet200ResponseToJSON(json: any): RestWallpapersWallpaperIdUploadersGet200Response {
    return RestWallpapersWallpaperIdUploadersGet200ResponseToJSONTyped(json, false);
}

export function RestWallpapersWallpaperIdUploadersGet200ResponseToJSONTyped(value?: RestWallpapersWallpaperIdUploadersGet200Response | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'results': ((value['results'] as Array<any>).map(RestWallpapersWallpaperIdUploadersGet200ResponseResultsInnerToJSON)),
        'page': value['page'],
        'page_size': value['pageSize'],
        'total': value['total'],
    };
}

