/* tslint:disable */
/* eslint-disable */
/**
 * Wallpapers API
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 0.1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface DashTopicsGet200ResponseTopicsInner
 */
export interface DashTopicsGet200ResponseTopicsInner {
    /**
     * 
     * @type {number}
     * @memberof DashTopicsGet200ResponseTopicsInner
     */
    id: number;
    /**
     * 
     * @type {string}
     * @memberof DashTopicsGet200ResponseTopicsInner
     */
    comment: string;
    /**
     * 
     * @type {number}
     * @memberof DashTopicsGet200ResponseTopicsInner
     */
    wallpaperCount: number;
    /**
     * 已发布统计
     * @type {number}
     * @memberof DashTopicsGet200ResponseTopicsInner
     */
    publishedCount: number;
}

/**
 * Check if a given object implements the DashTopicsGet200ResponseTopicsInner interface.
 */
export function instanceOfDashTopicsGet200ResponseTopicsInner(value: object): value is DashTopicsGet200ResponseTopicsInner {
    if (!('id' in value) || value['id'] === undefined) return false;
    if (!('comment' in value) || value['comment'] === undefined) return false;
    if (!('wallpaperCount' in value) || value['wallpaperCount'] === undefined) return false;
    if (!('publishedCount' in value) || value['publishedCount'] === undefined) return false;
    return true;
}

export function DashTopicsGet200ResponseTopicsInnerFromJSON(json: any): DashTopicsGet200ResponseTopicsInner {
    return DashTopicsGet200ResponseTopicsInnerFromJSONTyped(json, false);
}

export function DashTopicsGet200ResponseTopicsInnerFromJSONTyped(json: any, ignoreDiscriminator: boolean): DashTopicsGet200ResponseTopicsInner {
    if (json == null) {
        return json;
    }
    return {
        
        'id': json['id'],
        'comment': json['comment'],
        'wallpaperCount': json['wallpaper_count'],
        'publishedCount': json['published_count'],
    };
}

export function DashTopicsGet200ResponseTopicsInnerToJSON(json: any): DashTopicsGet200ResponseTopicsInner {
    return DashTopicsGet200ResponseTopicsInnerToJSONTyped(json, false);
}

export function DashTopicsGet200ResponseTopicsInnerToJSONTyped(value?: DashTopicsGet200ResponseTopicsInner | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'id': value['id'],
        'comment': value['comment'],
        'wallpaper_count': value['wallpaperCount'],
        'published_count': value['publishedCount'],
    };
}

