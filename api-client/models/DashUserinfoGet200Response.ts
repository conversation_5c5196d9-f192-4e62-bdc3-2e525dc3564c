/* tslint:disable */
/* eslint-disable */
/**
 * Wallpapers API
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 0.1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface DashUserinfoGet200Response
 */
export interface DashUserinfoGet200Response {
    /**
     * 
     * @type {string}
     * @memberof DashUserinfoGet200Response
     */
    email: string;
    /**
     * 是否为超级管理员 (如果是超管，则其默认拥有所有权限，不受 permissions 约束)
     * @type {boolean}
     * @memberof DashUserinfoGet200Response
     */
    isSuperuser: boolean;
    /**
     * 用户拥有的接口权限列表，为 operationId 集合。通过对照接口 operationId 判断是否有权限。
     * @type {Array<string>}
     * @memberof DashUserinfoGet200Response
     */
    permissions: Array<string>;
}

/**
 * Check if a given object implements the DashUserinfoGet200Response interface.
 */
export function instanceOfDashUserinfoGet200Response(value: object): value is DashUserinfoGet200Response {
    if (!('email' in value) || value['email'] === undefined) return false;
    if (!('isSuperuser' in value) || value['isSuperuser'] === undefined) return false;
    if (!('permissions' in value) || value['permissions'] === undefined) return false;
    return true;
}

export function DashUserinfoGet200ResponseFromJSON(json: any): DashUserinfoGet200Response {
    return DashUserinfoGet200ResponseFromJSONTyped(json, false);
}

export function DashUserinfoGet200ResponseFromJSONTyped(json: any, ignoreDiscriminator: boolean): DashUserinfoGet200Response {
    if (json == null) {
        return json;
    }
    return {
        
        'email': json['email'],
        'isSuperuser': json['is_superuser'],
        'permissions': json['permissions'],
    };
}

export function DashUserinfoGet200ResponseToJSON(json: any): DashUserinfoGet200Response {
    return DashUserinfoGet200ResponseToJSONTyped(json, false);
}

export function DashUserinfoGet200ResponseToJSONTyped(value?: DashUserinfoGet200Response | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'email': value['email'],
        'is_superuser': value['isSuperuser'],
        'permissions': value['permissions'],
    };
}

