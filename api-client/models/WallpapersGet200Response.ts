/* tslint:disable */
/* eslint-disable */
/**
 * Wallpapers API
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 0.1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { WallpapersGet200ResponseWallpapersInner } from './WallpapersGet200ResponseWallpapersInner';
import {
    WallpapersGet200ResponseWallpapersInnerFromJSON,
    WallpapersGet200ResponseWallpapersInnerFromJSONTyped,
    WallpapersGet200ResponseWallpapersInnerToJSON,
    WallpapersGet200ResponseWallpapersInnerToJSONTyped,
} from './WallpapersGet200ResponseWallpapersInner';

/**
 * 
 * @export
 * @interface WallpapersGet200Response
 */
export interface WallpapersGet200Response {
    /**
     * 
     * @type {Array<WallpapersGet200ResponseWallpapersInner>}
     * @memberof WallpapersGet200Response
     */
    wallpapers: Array<WallpapersGet200ResponseWallpapersInner>;
    /**
     * 
     * @type {number}
     * @memberof WallpapersGet200Response
     */
    currentPage: number;
    /**
     * 
     * @type {number}
     * @memberof WallpapersGet200Response
     */
    currentPageSize: number;
    /**
     * 
     * @type {number}
     * @memberof WallpapersGet200Response
     */
    total: number;
}

/**
 * Check if a given object implements the WallpapersGet200Response interface.
 */
export function instanceOfWallpapersGet200Response(value: object): value is WallpapersGet200Response {
    if (!('wallpapers' in value) || value['wallpapers'] === undefined) return false;
    if (!('currentPage' in value) || value['currentPage'] === undefined) return false;
    if (!('currentPageSize' in value) || value['currentPageSize'] === undefined) return false;
    if (!('total' in value) || value['total'] === undefined) return false;
    return true;
}

export function WallpapersGet200ResponseFromJSON(json: any): WallpapersGet200Response {
    return WallpapersGet200ResponseFromJSONTyped(json, false);
}

export function WallpapersGet200ResponseFromJSONTyped(json: any, ignoreDiscriminator: boolean): WallpapersGet200Response {
    if (json == null) {
        return json;
    }
    return {
        
        'wallpapers': ((json['wallpapers'] as Array<any>).map(WallpapersGet200ResponseWallpapersInnerFromJSON)),
        'currentPage': json['current_page'],
        'currentPageSize': json['current_page_size'],
        'total': json['total'],
    };
}

export function WallpapersGet200ResponseToJSON(json: any): WallpapersGet200Response {
    return WallpapersGet200ResponseToJSONTyped(json, false);
}

export function WallpapersGet200ResponseToJSONTyped(value?: WallpapersGet200Response | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'wallpapers': ((value['wallpapers'] as Array<any>).map(WallpapersGet200ResponseWallpapersInnerToJSON)),
        'current_page': value['currentPage'],
        'current_page_size': value['currentPageSize'],
        'total': value['total'],
    };
}

