/* tslint:disable */
/* eslint-disable */
/**
 * Wallpapers API
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 0.1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface PostUploadWallpapersRequest
 */
export interface PostUploadWallpapersRequest {
    /**
     * 
     * @type {Array<string>}
     * @memberof PostUploadWallpapersRequest
     */
    urls: Array<string>;
}

/**
 * Check if a given object implements the PostUploadWallpapersRequest interface.
 */
export function instanceOfPostUploadWallpapersRequest(value: object): value is PostUploadWallpapersRequest {
    if (!('urls' in value) || value['urls'] === undefined) return false;
    return true;
}

export function PostUploadWallpapersRequestFromJSON(json: any): PostUploadWallpapersRequest {
    return PostUploadWallpapersRequestFromJSONTyped(json, false);
}

export function PostUploadWallpapersRequestFromJSONTyped(json: any, ignoreDiscriminator: boolean): PostUploadWallpapersRequest {
    if (json == null) {
        return json;
    }
    return {
        
        'urls': json['urls'],
    };
}

export function PostUploadWallpapersRequestToJSON(json: any): PostUploadWallpapersRequest {
    return PostUploadWallpapersRequestToJSONTyped(json, false);
}

export function PostUploadWallpapersRequestToJSONTyped(value?: PostUploadWallpapersRequest | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'urls': value['urls'],
    };
}

