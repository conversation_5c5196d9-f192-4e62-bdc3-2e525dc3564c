/* tslint:disable */
/* eslint-disable */
/**
 * Wallpapers API
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 0.1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { WallpapersGet200ResponseWallpapersInnerImages } from './WallpapersGet200ResponseWallpapersInnerImages';
import {
    WallpapersGet200ResponseWallpapersInnerImagesFromJSON,
    WallpapersGet200ResponseWallpapersInnerImagesFromJSONTyped,
    WallpapersGet200ResponseWallpapersInnerImagesToJSON,
    WallpapersGet200ResponseWallpapersInnerImagesToJSONTyped,
} from './WallpapersGet200ResponseWallpapersInnerImages';

/**
 * 
 * @export
 * @interface DashTopicsTopicIdWallpapersGet200ResponseWallpapersInner
 */
export interface DashTopicsTopicIdWallpapersGet200ResponseWallpapersInner {
    /**
     * 
     * @type {WallpapersGet200ResponseWallpapersInnerImages}
     * @memberof DashTopicsTopicIdWallpapersGet200ResponseWallpapersInner
     */
    images: WallpapersGet200ResponseWallpapersInnerImages;
    /**
     * 
     * @type {string}
     * @memberof DashTopicsTopicIdWallpapersGet200ResponseWallpapersInner
     */
    format: string;
    /**
     * 
     * @type {number}
     * @memberof DashTopicsTopicIdWallpapersGet200ResponseWallpapersInner
     */
    width: number;
    /**
     * 
     * @type {number}
     * @memberof DashTopicsTopicIdWallpapersGet200ResponseWallpapersInner
     */
    height: number;
    /**
     * 单位为字节
     * @type {number}
     * @memberof DashTopicsTopicIdWallpapersGet200ResponseWallpapersInner
     */
    filesize: number;
    /**
     * 
     * @type {string}
     * @memberof DashTopicsTopicIdWallpapersGet200ResponseWallpapersInner
     */
    contentMd5: string;
    /**
     * 
     * @type {number}
     * @memberof DashTopicsTopicIdWallpapersGet200ResponseWallpapersInner
     */
    id: number;
}

/**
 * Check if a given object implements the DashTopicsTopicIdWallpapersGet200ResponseWallpapersInner interface.
 */
export function instanceOfDashTopicsTopicIdWallpapersGet200ResponseWallpapersInner(value: object): value is DashTopicsTopicIdWallpapersGet200ResponseWallpapersInner {
    if (!('images' in value) || value['images'] === undefined) return false;
    if (!('format' in value) || value['format'] === undefined) return false;
    if (!('width' in value) || value['width'] === undefined) return false;
    if (!('height' in value) || value['height'] === undefined) return false;
    if (!('filesize' in value) || value['filesize'] === undefined) return false;
    if (!('contentMd5' in value) || value['contentMd5'] === undefined) return false;
    if (!('id' in value) || value['id'] === undefined) return false;
    return true;
}

export function DashTopicsTopicIdWallpapersGet200ResponseWallpapersInnerFromJSON(json: any): DashTopicsTopicIdWallpapersGet200ResponseWallpapersInner {
    return DashTopicsTopicIdWallpapersGet200ResponseWallpapersInnerFromJSONTyped(json, false);
}

export function DashTopicsTopicIdWallpapersGet200ResponseWallpapersInnerFromJSONTyped(json: any, ignoreDiscriminator: boolean): DashTopicsTopicIdWallpapersGet200ResponseWallpapersInner {
    if (json == null) {
        return json;
    }
    return {
        
        'images': WallpapersGet200ResponseWallpapersInnerImagesFromJSON(json['images']),
        'format': json['format'],
        'width': json['width'],
        'height': json['height'],
        'filesize': json['filesize'],
        'contentMd5': json['content_md5'],
        'id': json['id'],
    };
}

export function DashTopicsTopicIdWallpapersGet200ResponseWallpapersInnerToJSON(json: any): DashTopicsTopicIdWallpapersGet200ResponseWallpapersInner {
    return DashTopicsTopicIdWallpapersGet200ResponseWallpapersInnerToJSONTyped(json, false);
}

export function DashTopicsTopicIdWallpapersGet200ResponseWallpapersInnerToJSONTyped(value?: DashTopicsTopicIdWallpapersGet200ResponseWallpapersInner | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'images': WallpapersGet200ResponseWallpapersInnerImagesToJSON(value['images']),
        'format': value['format'],
        'width': value['width'],
        'height': value['height'],
        'filesize': value['filesize'],
        'content_md5': value['contentMd5'],
        'id': value['id'],
    };
}

