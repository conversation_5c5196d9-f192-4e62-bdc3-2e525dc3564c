/* tslint:disable */
/* eslint-disable */
/**
 * Wallpapers API
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 0.1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface DashClienttopicsClienttopicIdPutRequest
 */
export interface DashClienttopicsClienttopicIdPutRequest {
    /**
     * 
     * @type {string}
     * @memberof DashClienttopicsClienttopicIdPutRequest
     */
    title: string;
}

/**
 * Check if a given object implements the DashClienttopicsClienttopicIdPutRequest interface.
 */
export function instanceOfDashClienttopicsClienttopicIdPutRequest(value: object): value is DashClienttopicsClienttopicIdPutRequest {
    if (!('title' in value) || value['title'] === undefined) return false;
    return true;
}

export function DashClienttopicsClienttopicIdPutRequestFromJSON(json: any): DashClienttopicsClienttopicIdPutRequest {
    return DashClienttopicsClienttopicIdPutRequestFromJSONTyped(json, false);
}

export function DashClienttopicsClienttopicIdPutRequestFromJSONTyped(json: any, ignoreDiscriminator: boolean): DashClienttopicsClienttopicIdPutRequest {
    if (json == null) {
        return json;
    }
    return {
        
        'title': json['title'],
    };
}

export function DashClienttopicsClienttopicIdPutRequestToJSON(json: any): DashClienttopicsClienttopicIdPutRequest {
    return DashClienttopicsClienttopicIdPutRequestToJSONTyped(json, false);
}

export function DashClienttopicsClienttopicIdPutRequestToJSONTyped(value?: DashClienttopicsClienttopicIdPutRequest | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'title': value['title'],
    };
}

