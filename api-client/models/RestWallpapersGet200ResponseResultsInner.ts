/* tslint:disable */
/* eslint-disable */
/**
 * Wallpapers API
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 0.1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { RestWallpapersGet200ResponseResultsInnerImages } from './RestWallpapersGet200ResponseResultsInnerImages';
import {
    RestWallpapersGet200ResponseResultsInnerImagesFromJSON,
    RestWallpapersGet200ResponseResultsInnerImagesFromJSONTyped,
    RestWallpapersGet200ResponseResultsInnerImagesToJSON,
    RestWallpapersGet200ResponseResultsInnerImagesToJSONTyped,
} from './RestWallpapersGet200ResponseResultsInnerImages';

/**
 * 
 * @export
 * @interface RestWallpapersGet200ResponseResultsInner
 */
export interface RestWallpapersGet200ResponseResultsInner {
    /**
     * 
     * @type {number}
     * @memberof RestWallpapersGet200ResponseResultsInner
     */
    id: number;
    /**
     * 原始图片地址，该字段需要管理员身份才能获取
     * @type {string}
     * @memberof RestWallpapersGet200ResponseResultsInner
     */
    url?: string;
    /**
     * 
     * @type {RestWallpapersGet200ResponseResultsInnerImages}
     * @memberof RestWallpapersGet200ResponseResultsInner
     */
    images: RestWallpapersGet200ResponseResultsInnerImages;
    /**
     * 
     * @type {string}
     * @memberof RestWallpapersGet200ResponseResultsInner
     */
    format: string;
    /**
     * 
     * @type {number}
     * @memberof RestWallpapersGet200ResponseResultsInner
     */
    width: number;
    /**
     * 
     * @type {number}
     * @memberof RestWallpapersGet200ResponseResultsInner
     */
    height: number;
    /**
     * 单位为字节
     * @type {number}
     * @memberof RestWallpapersGet200ResponseResultsInner
     */
    filesize: number;
    /**
     * 
     * @type {string}
     * @memberof RestWallpapersGet200ResponseResultsInner
     */
    contentMd5: string;
}

/**
 * Check if a given object implements the RestWallpapersGet200ResponseResultsInner interface.
 */
export function instanceOfRestWallpapersGet200ResponseResultsInner(value: object): value is RestWallpapersGet200ResponseResultsInner {
    if (!('id' in value) || value['id'] === undefined) return false;
    if (!('images' in value) || value['images'] === undefined) return false;
    if (!('format' in value) || value['format'] === undefined) return false;
    if (!('width' in value) || value['width'] === undefined) return false;
    if (!('height' in value) || value['height'] === undefined) return false;
    if (!('filesize' in value) || value['filesize'] === undefined) return false;
    if (!('contentMd5' in value) || value['contentMd5'] === undefined) return false;
    return true;
}

export function RestWallpapersGet200ResponseResultsInnerFromJSON(json: any): RestWallpapersGet200ResponseResultsInner {
    return RestWallpapersGet200ResponseResultsInnerFromJSONTyped(json, false);
}

export function RestWallpapersGet200ResponseResultsInnerFromJSONTyped(json: any, ignoreDiscriminator: boolean): RestWallpapersGet200ResponseResultsInner {
    if (json == null) {
        return json;
    }
    return {
        
        'id': json['id'],
        'url': json['url'] == null ? undefined : json['url'],
        'images': RestWallpapersGet200ResponseResultsInnerImagesFromJSON(json['images']),
        'format': json['format'],
        'width': json['width'],
        'height': json['height'],
        'filesize': json['filesize'],
        'contentMd5': json['content_md5'],
    };
}

export function RestWallpapersGet200ResponseResultsInnerToJSON(json: any): RestWallpapersGet200ResponseResultsInner {
    return RestWallpapersGet200ResponseResultsInnerToJSONTyped(json, false);
}

export function RestWallpapersGet200ResponseResultsInnerToJSONTyped(value?: RestWallpapersGet200ResponseResultsInner | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'id': value['id'],
        'url': value['url'],
        'images': RestWallpapersGet200ResponseResultsInnerImagesToJSON(value['images']),
        'format': value['format'],
        'width': value['width'],
        'height': value['height'],
        'filesize': value['filesize'],
        'content_md5': value['contentMd5'],
    };
}

