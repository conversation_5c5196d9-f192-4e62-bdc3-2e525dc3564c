/* tslint:disable */
/* eslint-disable */
/**
 * Wallpapers API
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 0.1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { DashSearchWallpapersGet200ResponseResultsInnerWallpaper } from './DashSearchWallpapersGet200ResponseResultsInnerWallpaper';
import {
    DashSearchWallpapersGet200ResponseResultsInnerWallpaperFromJSON,
    DashSearchWallpapersGet200ResponseResultsInnerWallpaperFromJSONTyped,
    DashSearchWallpapersGet200ResponseResultsInnerWallpaperToJSON,
    DashSearchWallpapersGet200ResponseResultsInnerWallpaperToJSONTyped,
} from './DashSearchWallpapersGet200ResponseResultsInnerWallpaper';

/**
 * 
 * @export
 * @interface DashSearchWallpapersGet200ResponseResultsInner
 */
export interface DashSearchWallpapersGet200ResponseResultsInner {
    /**
     * 
     * @type {number}
     * @memberof DashSearchWallpapersGet200ResponseResultsInner
     */
    id: number;
    /**
     * 
     * @type {DashSearchWallpapersGet200ResponseResultsInnerWallpaper}
     * @memberof DashSearchWallpapersGet200ResponseResultsInner
     */
    wallpaper: DashSearchWallpapersGet200ResponseResultsInnerWallpaper;
}

/**
 * Check if a given object implements the DashSearchWallpapersGet200ResponseResultsInner interface.
 */
export function instanceOfDashSearchWallpapersGet200ResponseResultsInner(value: object): value is DashSearchWallpapersGet200ResponseResultsInner {
    if (!('id' in value) || value['id'] === undefined) return false;
    if (!('wallpaper' in value) || value['wallpaper'] === undefined) return false;
    return true;
}

export function DashSearchWallpapersGet200ResponseResultsInnerFromJSON(json: any): DashSearchWallpapersGet200ResponseResultsInner {
    return DashSearchWallpapersGet200ResponseResultsInnerFromJSONTyped(json, false);
}

export function DashSearchWallpapersGet200ResponseResultsInnerFromJSONTyped(json: any, ignoreDiscriminator: boolean): DashSearchWallpapersGet200ResponseResultsInner {
    if (json == null) {
        return json;
    }
    return {
        
        'id': json['id'],
        'wallpaper': DashSearchWallpapersGet200ResponseResultsInnerWallpaperFromJSON(json['wallpaper']),
    };
}

export function DashSearchWallpapersGet200ResponseResultsInnerToJSON(json: any): DashSearchWallpapersGet200ResponseResultsInner {
    return DashSearchWallpapersGet200ResponseResultsInnerToJSONTyped(json, false);
}

export function DashSearchWallpapersGet200ResponseResultsInnerToJSONTyped(value?: DashSearchWallpapersGet200ResponseResultsInner | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'id': value['id'],
        'wallpaper': DashSearchWallpapersGet200ResponseResultsInnerWallpaperToJSON(value['wallpaper']),
    };
}

