/* tslint:disable */
/* eslint-disable */
/**
 * Wallpapers API
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 0.1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface DashTopicpublishedGet200ResponsePublishedInnerClient
 */
export interface DashTopicpublishedGet200ResponsePublishedInnerClient {
    /**
     * 
     * @type {string}
     * @memberof DashTopicpublishedGet200ResponsePublishedInnerClient
     */
    id: string;
    /**
     * 
     * @type {string}
     * @memberof DashTopicpublishedGet200ResponsePublishedInnerClient
     */
    name: string;
}

/**
 * Check if a given object implements the DashTopicpublishedGet200ResponsePublishedInnerClient interface.
 */
export function instanceOfDashTopicpublishedGet200ResponsePublishedInnerClient(value: object): value is DashTopicpublishedGet200ResponsePublishedInnerClient {
    if (!('id' in value) || value['id'] === undefined) return false;
    if (!('name' in value) || value['name'] === undefined) return false;
    return true;
}

export function DashTopicpublishedGet200ResponsePublishedInnerClientFromJSON(json: any): DashTopicpublishedGet200ResponsePublishedInnerClient {
    return DashTopicpublishedGet200ResponsePublishedInnerClientFromJSONTyped(json, false);
}

export function DashTopicpublishedGet200ResponsePublishedInnerClientFromJSONTyped(json: any, ignoreDiscriminator: boolean): DashTopicpublishedGet200ResponsePublishedInnerClient {
    if (json == null) {
        return json;
    }
    return {
        
        'id': json['id'],
        'name': json['name'],
    };
}

export function DashTopicpublishedGet200ResponsePublishedInnerClientToJSON(json: any): DashTopicpublishedGet200ResponsePublishedInnerClient {
    return DashTopicpublishedGet200ResponsePublishedInnerClientToJSONTyped(json, false);
}

export function DashTopicpublishedGet200ResponsePublishedInnerClientToJSONTyped(value?: DashTopicpublishedGet200ResponsePublishedInnerClient | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'id': value['id'],
        'name': value['name'],
    };
}

