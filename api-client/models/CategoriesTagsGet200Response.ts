/* tslint:disable */
/* eslint-disable */
/**
 * Wallpapers API
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 0.1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { CategoriesTagsGet200ResponseTagsInner } from './CategoriesTagsGet200ResponseTagsInner';
import {
    CategoriesTagsGet200ResponseTagsInnerFromJSON,
    CategoriesTagsGet200ResponseTagsInnerFromJSONTyped,
    CategoriesTagsGet200ResponseTagsInnerToJSON,
    CategoriesTagsGet200ResponseTagsInnerToJSONTyped,
} from './CategoriesTagsGet200ResponseTagsInner';

/**
 * 
 * @export
 * @interface CategoriesTagsGet200Response
 */
export interface CategoriesTagsGet200Response {
    /**
     * 
     * @type {Array<CategoriesTagsGet200ResponseTagsInner>}
     * @memberof CategoriesTagsGet200Response
     */
    tags: Array<CategoriesTagsGet200ResponseTagsInner>;
}

/**
 * Check if a given object implements the CategoriesTagsGet200Response interface.
 */
export function instanceOfCategoriesTagsGet200Response(value: object): value is CategoriesTagsGet200Response {
    if (!('tags' in value) || value['tags'] === undefined) return false;
    return true;
}

export function CategoriesTagsGet200ResponseFromJSON(json: any): CategoriesTagsGet200Response {
    return CategoriesTagsGet200ResponseFromJSONTyped(json, false);
}

export function CategoriesTagsGet200ResponseFromJSONTyped(json: any, ignoreDiscriminator: boolean): CategoriesTagsGet200Response {
    if (json == null) {
        return json;
    }
    return {
        
        'tags': ((json['tags'] as Array<any>).map(CategoriesTagsGet200ResponseTagsInnerFromJSON)),
    };
}

export function CategoriesTagsGet200ResponseToJSON(json: any): CategoriesTagsGet200Response {
    return CategoriesTagsGet200ResponseToJSONTyped(json, false);
}

export function CategoriesTagsGet200ResponseToJSONTyped(value?: CategoriesTagsGet200Response | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'tags': ((value['tags'] as Array<any>).map(CategoriesTagsGet200ResponseTagsInnerToJSON)),
    };
}

