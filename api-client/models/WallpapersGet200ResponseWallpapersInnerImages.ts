/* tslint:disable */
/* eslint-disable */
/**
 * Wallpapers API
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 0.1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 图片列表，当未提供任何 size 时，将默认填充一个 default 字段
 * @export
 * @interface WallpapersGet200ResponseWallpapersInnerImages
 */
export interface WallpapersGet200ResponseWallpapersInnerImages {
    [key: string]: string | any;
    /**
     * 
     * @type {string}
     * @memberof WallpapersGet200ResponseWallpapersInnerImages
     */
    _default?: string;
}

/**
 * Check if a given object implements the WallpapersGet200ResponseWallpapersInnerImages interface.
 */
export function instanceOfWallpapersGet200ResponseWallpapersInnerImages(value: object): value is WallpapersGet200ResponseWallpapersInnerImages {
    return true;
}

export function WallpapersGet200ResponseWallpapersInnerImagesFromJSON(json: any): WallpapersGet200ResponseWallpapersInnerImages {
    return WallpapersGet200ResponseWallpapersInnerImagesFromJSONTyped(json, false);
}

export function WallpapersGet200ResponseWallpapersInnerImagesFromJSONTyped(json: any, ignoreDiscriminator: boolean): WallpapersGet200ResponseWallpapersInnerImages {
    if (json == null) {
        return json;
    }
    return {
        
            ...json,
        '_default': json['default'] == null ? undefined : json['default'],
    };
}

export function WallpapersGet200ResponseWallpapersInnerImagesToJSON(json: any): WallpapersGet200ResponseWallpapersInnerImages {
    return WallpapersGet200ResponseWallpapersInnerImagesToJSONTyped(json, false);
}

export function WallpapersGet200ResponseWallpapersInnerImagesToJSONTyped(value?: WallpapersGet200ResponseWallpapersInnerImages | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
            ...value,
        'default': value['_default'],
    };
}

