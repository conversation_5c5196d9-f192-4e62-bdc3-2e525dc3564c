/* tslint:disable */
/* eslint-disable */
/**
 * Wallpapers API
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 0.1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface WallpapersGet200ResponseWallpapersInnerUploader
 */
export interface WallpapersGet200ResponseWallpapersInnerUploader {
    /**
     * 
     * @type {string}
     * @memberof WallpapersGet200ResponseWallpapersInnerUploader
     */
    name: string;
}

/**
 * Check if a given object implements the WallpapersGet200ResponseWallpapersInnerUploader interface.
 */
export function instanceOfWallpapersGet200ResponseWallpapersInnerUploader(value: object): value is WallpapersGet200ResponseWallpapersInnerUploader {
    if (!('name' in value) || value['name'] === undefined) return false;
    return true;
}

export function WallpapersGet200ResponseWallpapersInnerUploaderFromJSON(json: any): WallpapersGet200ResponseWallpapersInnerUploader {
    return WallpapersGet200ResponseWallpapersInnerUploaderFromJSONTyped(json, false);
}

export function WallpapersGet200ResponseWallpapersInnerUploaderFromJSONTyped(json: any, ignoreDiscriminator: boolean): WallpapersGet200ResponseWallpapersInnerUploader {
    if (json == null) {
        return json;
    }
    return {
        
        'name': json['name'],
    };
}

export function WallpapersGet200ResponseWallpapersInnerUploaderToJSON(json: any): WallpapersGet200ResponseWallpapersInnerUploader {
    return WallpapersGet200ResponseWallpapersInnerUploaderToJSONTyped(json, false);
}

export function WallpapersGet200ResponseWallpapersInnerUploaderToJSONTyped(value?: WallpapersGet200ResponseWallpapersInnerUploader | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'name': value['name'],
    };
}

