/* tslint:disable */
/* eslint-disable */
/**
 * Wallpapers API
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 0.1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 图片列表，当未提供任何 size 时，将默认填充一个 default 字段
 * @export
 * @interface RestWallpapersGet200ResponseResultsInnerImages
 */
export interface RestWallpapersGet200ResponseResultsInnerImages {
    [key: string]: string | any;
    /**
     * 
     * @type {string}
     * @memberof RestWallpapersGet200ResponseResultsInnerImages
     */
    _default?: string;
}

/**
 * Check if a given object implements the RestWallpapersGet200ResponseResultsInnerImages interface.
 */
export function instanceOfRestWallpapersGet200ResponseResultsInnerImages(value: object): value is RestWallpapersGet200ResponseResultsInnerImages {
    return true;
}

export function RestWallpapersGet200ResponseResultsInnerImagesFromJSON(json: any): RestWallpapersGet200ResponseResultsInnerImages {
    return RestWallpapersGet200ResponseResultsInnerImagesFromJSONTyped(json, false);
}

export function RestWallpapersGet200ResponseResultsInnerImagesFromJSONTyped(json: any, ignoreDiscriminator: boolean): RestWallpapersGet200ResponseResultsInnerImages {
    if (json == null) {
        return json;
    }
    return {
        
            ...json,
        '_default': json['default'] == null ? undefined : json['default'],
    };
}

export function RestWallpapersGet200ResponseResultsInnerImagesToJSON(json: any): RestWallpapersGet200ResponseResultsInnerImages {
    return RestWallpapersGet200ResponseResultsInnerImagesToJSONTyped(json, false);
}

export function RestWallpapersGet200ResponseResultsInnerImagesToJSONTyped(value?: RestWallpapersGet200ResponseResultsInnerImages | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
            ...value,
        'default': value['_default'],
    };
}

