/* tslint:disable */
/* eslint-disable */
/**
 * Wallpapers API
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 0.1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { DashTopicsGet200ResponseResultsInner } from './DashTopicsGet200ResponseResultsInner';
import {
    DashTopicsGet200ResponseResultsInnerFromJSON,
    DashTopicsGet200ResponseResultsInnerFromJSONTyped,
    DashTopicsGet200ResponseResultsInnerToJSON,
    DashTopicsGet200ResponseResultsInnerToJSONTyped,
} from './DashTopicsGet200ResponseResultsInner';

/**
 * 
 * @export
 * @interface DashTopicsGet200Response
 */
export interface DashTopicsGet200Response {
    /**
     * 
     * @type {Array<DashTopicsGet200ResponseResultsInner>}
     * @memberof DashTopicsGet200Response
     */
    results: Array<DashTopicsGet200ResponseResultsInner>;
    /**
     * 
     * @type {number}
     * @memberof DashTopicsGet200Response
     */
    page: number;
    /**
     * 
     * @type {number}
     * @memberof DashTopicsGet200Response
     */
    pageSize: number;
    /**
     * 
     * @type {number}
     * @memberof DashTopicsGet200Response
     */
    total: number;
}

/**
 * Check if a given object implements the DashTopicsGet200Response interface.
 */
export function instanceOfDashTopicsGet200Response(value: object): value is DashTopicsGet200Response {
    if (!('results' in value) || value['results'] === undefined) return false;
    if (!('page' in value) || value['page'] === undefined) return false;
    if (!('pageSize' in value) || value['pageSize'] === undefined) return false;
    if (!('total' in value) || value['total'] === undefined) return false;
    return true;
}

export function DashTopicsGet200ResponseFromJSON(json: any): DashTopicsGet200Response {
    return DashTopicsGet200ResponseFromJSONTyped(json, false);
}

export function DashTopicsGet200ResponseFromJSONTyped(json: any, ignoreDiscriminator: boolean): DashTopicsGet200Response {
    if (json == null) {
        return json;
    }
    return {
        
        'results': ((json['results'] as Array<any>).map(DashTopicsGet200ResponseResultsInnerFromJSON)),
        'page': json['page'],
        'pageSize': json['page_size'],
        'total': json['total'],
    };
}

export function DashTopicsGet200ResponseToJSON(json: any): DashTopicsGet200Response {
    return DashTopicsGet200ResponseToJSONTyped(json, false);
}

export function DashTopicsGet200ResponseToJSONTyped(value?: DashTopicsGet200Response | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'results': ((value['results'] as Array<any>).map(DashTopicsGet200ResponseResultsInnerToJSON)),
        'page': value['page'],
        'page_size': value['pageSize'],
        'total': value['total'],
    };
}

