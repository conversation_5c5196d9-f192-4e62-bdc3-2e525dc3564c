/* tslint:disable */
/* eslint-disable */
/**
 * Wallpapers API
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 0.1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface CategoriesTagsGet200ResponseTagsInner
 */
export interface CategoriesTagsGet200ResponseTagsInner {
    /**
     * 
     * @type {string}
     * @memberof CategoriesTagsGet200ResponseTagsInner
     */
    name: string;
    /**
     * 
     * @type {string}
     * @memberof CategoriesTagsGet200ResponseTagsInner
     */
    posterUrl: string | null;
}

/**
 * Check if a given object implements the CategoriesTagsGet200ResponseTagsInner interface.
 */
export function instanceOfCategoriesTagsGet200ResponseTagsInner(value: object): value is CategoriesTagsGet200ResponseTagsInner {
    if (!('name' in value) || value['name'] === undefined) return false;
    if (!('posterUrl' in value) || value['posterUrl'] === undefined) return false;
    return true;
}

export function CategoriesTagsGet200ResponseTagsInnerFromJSON(json: any): CategoriesTagsGet200ResponseTagsInner {
    return CategoriesTagsGet200ResponseTagsInnerFromJSONTyped(json, false);
}

export function CategoriesTagsGet200ResponseTagsInnerFromJSONTyped(json: any, ignoreDiscriminator: boolean): CategoriesTagsGet200ResponseTagsInner {
    if (json == null) {
        return json;
    }
    return {
        
        'name': json['name'],
        'posterUrl': json['posterUrl'],
    };
}

export function CategoriesTagsGet200ResponseTagsInnerToJSON(json: any): CategoriesTagsGet200ResponseTagsInner {
    return CategoriesTagsGet200ResponseTagsInnerToJSONTyped(json, false);
}

export function CategoriesTagsGet200ResponseTagsInnerToJSONTyped(value?: CategoriesTagsGet200ResponseTagsInner | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'name': value['name'],
        'posterUrl': value['posterUrl'],
    };
}

