/* tslint:disable */
/* eslint-disable */
/**
 * Wallpapers API
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 0.1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { RestWallpapersGet200ResponseResultsInnerImages } from './RestWallpapersGet200ResponseResultsInnerImages';
import {
    RestWallpapersGet200ResponseResultsInnerImagesFromJSON,
    RestWallpapersGet200ResponseResultsInnerImagesFromJSONTyped,
    RestWallpapersGet200ResponseResultsInnerImagesToJSON,
    RestWallpapersGet200ResponseResultsInnerImagesToJSONTyped,
} from './RestWallpapersGet200ResponseResultsInnerImages';

/**
 * 
 * @export
 * @interface DashSearchWallpapersV2Get200ResponseWallpapersInner
 */
export interface DashSearchWallpapersV2Get200ResponseWallpapersInner {
    /**
     * 
     * @type {number}
     * @memberof DashSearchWallpapersV2Get200ResponseWallpapersInner
     */
    id: number;
    /**
     * 
     * @type {RestWallpapersGet200ResponseResultsInnerImages}
     * @memberof DashSearchWallpapersV2Get200ResponseWallpapersInner
     */
    images: RestWallpapersGet200ResponseResultsInnerImages;
    /**
     * 
     * @type {string}
     * @memberof DashSearchWallpapersV2Get200ResponseWallpapersInner
     */
    format: string;
    /**
     * 
     * @type {number}
     * @memberof DashSearchWallpapersV2Get200ResponseWallpapersInner
     */
    width: number;
    /**
     * 
     * @type {number}
     * @memberof DashSearchWallpapersV2Get200ResponseWallpapersInner
     */
    height: number;
    /**
     * 单位为字节
     * @type {number}
     * @memberof DashSearchWallpapersV2Get200ResponseWallpapersInner
     */
    filesize: number;
    /**
     * 
     * @type {string}
     * @memberof DashSearchWallpapersV2Get200ResponseWallpapersInner
     */
    contentMd5: string;
    /**
     * 搜索得分
     * @type {number}
     * @memberof DashSearchWallpapersV2Get200ResponseWallpapersInner
     */
    searchScore: number;
}

/**
 * Check if a given object implements the DashSearchWallpapersV2Get200ResponseWallpapersInner interface.
 */
export function instanceOfDashSearchWallpapersV2Get200ResponseWallpapersInner(value: object): value is DashSearchWallpapersV2Get200ResponseWallpapersInner {
    if (!('id' in value) || value['id'] === undefined) return false;
    if (!('images' in value) || value['images'] === undefined) return false;
    if (!('format' in value) || value['format'] === undefined) return false;
    if (!('width' in value) || value['width'] === undefined) return false;
    if (!('height' in value) || value['height'] === undefined) return false;
    if (!('filesize' in value) || value['filesize'] === undefined) return false;
    if (!('contentMd5' in value) || value['contentMd5'] === undefined) return false;
    if (!('searchScore' in value) || value['searchScore'] === undefined) return false;
    return true;
}

export function DashSearchWallpapersV2Get200ResponseWallpapersInnerFromJSON(json: any): DashSearchWallpapersV2Get200ResponseWallpapersInner {
    return DashSearchWallpapersV2Get200ResponseWallpapersInnerFromJSONTyped(json, false);
}

export function DashSearchWallpapersV2Get200ResponseWallpapersInnerFromJSONTyped(json: any, ignoreDiscriminator: boolean): DashSearchWallpapersV2Get200ResponseWallpapersInner {
    if (json == null) {
        return json;
    }
    return {
        
        'id': json['id'],
        'images': RestWallpapersGet200ResponseResultsInnerImagesFromJSON(json['images']),
        'format': json['format'],
        'width': json['width'],
        'height': json['height'],
        'filesize': json['filesize'],
        'contentMd5': json['content_md5'],
        'searchScore': json['search_score'],
    };
}

export function DashSearchWallpapersV2Get200ResponseWallpapersInnerToJSON(json: any): DashSearchWallpapersV2Get200ResponseWallpapersInner {
    return DashSearchWallpapersV2Get200ResponseWallpapersInnerToJSONTyped(json, false);
}

export function DashSearchWallpapersV2Get200ResponseWallpapersInnerToJSONTyped(value?: DashSearchWallpapersV2Get200ResponseWallpapersInner | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'id': value['id'],
        'images': RestWallpapersGet200ResponseResultsInnerImagesToJSON(value['images']),
        'format': value['format'],
        'width': value['width'],
        'height': value['height'],
        'filesize': value['filesize'],
        'content_md5': value['contentMd5'],
        'search_score': value['searchScore'],
    };
}

