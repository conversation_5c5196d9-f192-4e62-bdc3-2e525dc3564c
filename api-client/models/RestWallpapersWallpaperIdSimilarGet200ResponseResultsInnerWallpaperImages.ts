/* tslint:disable */
/* eslint-disable */
/**
 * Wallpapers API
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 0.1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 图片列表，当未提供任何 size 时，将默认填充一个 default 字段
 * @export
 * @interface RestWallpapersWallpaperIdSimilarGet200ResponseResultsInnerWallpaperImages
 */
export interface RestWallpapersWallpaperIdSimilarGet200ResponseResultsInnerWallpaperImages {
    [key: string]: string | any;
    /**
     * 
     * @type {string}
     * @memberof RestWallpapersWallpaperIdSimilarGet200ResponseResultsInnerWallpaperImages
     */
    _default?: string;
}

/**
 * Check if a given object implements the RestWallpapersWallpaperIdSimilarGet200ResponseResultsInnerWallpaperImages interface.
 */
export function instanceOfRestWallpapersWallpaperIdSimilarGet200ResponseResultsInnerWallpaperImages(value: object): value is RestWallpapersWallpaperIdSimilarGet200ResponseResultsInnerWallpaperImages {
    return true;
}

export function RestWallpapersWallpaperIdSimilarGet200ResponseResultsInnerWallpaperImagesFromJSON(json: any): RestWallpapersWallpaperIdSimilarGet200ResponseResultsInnerWallpaperImages {
    return RestWallpapersWallpaperIdSimilarGet200ResponseResultsInnerWallpaperImagesFromJSONTyped(json, false);
}

export function RestWallpapersWallpaperIdSimilarGet200ResponseResultsInnerWallpaperImagesFromJSONTyped(json: any, ignoreDiscriminator: boolean): RestWallpapersWallpaperIdSimilarGet200ResponseResultsInnerWallpaperImages {
    if (json == null) {
        return json;
    }
    return {
        
            ...json,
        '_default': json['default'] == null ? undefined : json['default'],
    };
}

export function RestWallpapersWallpaperIdSimilarGet200ResponseResultsInnerWallpaperImagesToJSON(json: any): RestWallpapersWallpaperIdSimilarGet200ResponseResultsInnerWallpaperImages {
    return RestWallpapersWallpaperIdSimilarGet200ResponseResultsInnerWallpaperImagesToJSONTyped(json, false);
}

export function RestWallpapersWallpaperIdSimilarGet200ResponseResultsInnerWallpaperImagesToJSONTyped(value?: RestWallpapersWallpaperIdSimilarGet200ResponseResultsInnerWallpaperImages | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
            ...value,
        'default': value['_default'],
    };
}

