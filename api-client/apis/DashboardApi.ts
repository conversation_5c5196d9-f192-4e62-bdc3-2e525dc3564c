/* tslint:disable */
/* eslint-disable */
/**
 * Wallpapers API
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 0.1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import * as runtime from '../runtime';
import type {
  DashAuthTokenPost200Response,
  DashClientsGet200Response,
  DashClienttopicsClienttopicIdPut200Response,
  DashClienttopicsClienttopicIdPutRequest,
  DashSearchWallpapersGet200Response,
  DashSearchWallpapersV2Get200Response,
  DashStatsGet200Response,
  DashTopicTopicIdPublishClientIdPostRequest,
  DashTopicsGet200Response,
  DashTopicsGet200ResponseResultsInner,
  DashTopicsPostRequest,
  DashTopicsTopicIdClienttopicsGet200Response,
  DashTopicsTopicIdGet200Response,
  DashTopicsTopicIdSuggestionsTagsGet200Response,
  DashTopicsTopicIdWallpapersGet200Response,
  DashTopicsTopicIdWallpapersPostRequest,
  DashUserinfoGet200Response,
  DashWallpapersWallpaperIdSimilarGet200Response,
  DashWallpapersWallpaperIdTopicsGet200Response,
  PostUploadWallpapersRequest,
} from '../models/index';
import {
    DashAuthTokenPost200ResponseFromJSON,
    DashAuthTokenPost200ResponseToJSON,
    DashClientsGet200ResponseFromJSON,
    DashClientsGet200ResponseToJSON,
    DashClienttopicsClienttopicIdPut200ResponseFromJSON,
    DashClienttopicsClienttopicIdPut200ResponseToJSON,
    DashClienttopicsClienttopicIdPutRequestFromJSON,
    DashClienttopicsClienttopicIdPutRequestToJSON,
    DashSearchWallpapersGet200ResponseFromJSON,
    DashSearchWallpapersGet200ResponseToJSON,
    DashSearchWallpapersV2Get200ResponseFromJSON,
    DashSearchWallpapersV2Get200ResponseToJSON,
    DashStatsGet200ResponseFromJSON,
    DashStatsGet200ResponseToJSON,
    DashTopicTopicIdPublishClientIdPostRequestFromJSON,
    DashTopicTopicIdPublishClientIdPostRequestToJSON,
    DashTopicsGet200ResponseFromJSON,
    DashTopicsGet200ResponseToJSON,
    DashTopicsGet200ResponseResultsInnerFromJSON,
    DashTopicsGet200ResponseResultsInnerToJSON,
    DashTopicsPostRequestFromJSON,
    DashTopicsPostRequestToJSON,
    DashTopicsTopicIdClienttopicsGet200ResponseFromJSON,
    DashTopicsTopicIdClienttopicsGet200ResponseToJSON,
    DashTopicsTopicIdGet200ResponseFromJSON,
    DashTopicsTopicIdGet200ResponseToJSON,
    DashTopicsTopicIdSuggestionsTagsGet200ResponseFromJSON,
    DashTopicsTopicIdSuggestionsTagsGet200ResponseToJSON,
    DashTopicsTopicIdWallpapersGet200ResponseFromJSON,
    DashTopicsTopicIdWallpapersGet200ResponseToJSON,
    DashTopicsTopicIdWallpapersPostRequestFromJSON,
    DashTopicsTopicIdWallpapersPostRequestToJSON,
    DashUserinfoGet200ResponseFromJSON,
    DashUserinfoGet200ResponseToJSON,
    DashWallpapersWallpaperIdSimilarGet200ResponseFromJSON,
    DashWallpapersWallpaperIdSimilarGet200ResponseToJSON,
    DashWallpapersWallpaperIdTopicsGet200ResponseFromJSON,
    DashWallpapersWallpaperIdTopicsGet200ResponseToJSON,
    PostUploadWallpapersRequestFromJSON,
    PostUploadWallpapersRequestToJSON,
} from '../models/index';

export interface DashAuthTokenPostRequest {
    cFTurnstileResponse: string;
    username: string;
    password: string;
}

export interface DashClientsGetRequest {
    fields?: Array<DashClientsGetFieldsEnum>;
    page?: number;
    pageSize?: number;
}

export interface DashClienttopicsClienttopicIdDeleteRequest {
    clienttopicId: number;
}

export interface DashClienttopicsClienttopicIdPutOperationRequest {
    clienttopicId: number;
    dashClienttopicsClienttopicIdPutRequest: DashClienttopicsClienttopicIdPutRequest;
}

export interface DashClienttopicsGetRequest {
    page?: number;
    pageSize?: number;
    fields?: Array<DashClienttopicsGetFieldsEnum>;
    clientId?: string;
}

export interface DashSearchWallpapersGetRequest {
    q: string;
    after?: number;
    limit?: number;
    size?: Array<string>;
    excludeTopicId?: number;
}

export interface DashSearchWallpapersV2GetRequest {
    q: string;
    sizedFor?: DashSearchWallpapersV2GetSizedForEnum;
}

export interface DashTopicTopicIdPublishClientIdPostOperationRequest {
    topicId: number;
    clientId: string;
    dashTopicTopicIdPublishClientIdPostRequest: DashTopicTopicIdPublishClientIdPostRequest;
}

export interface DashTopicsGetRequest {
    page?: number;
    pageSize?: number;
    excludeWallpaperId?: number;
    inClient?: boolean;
}

export interface DashTopicsPostOperationRequest {
    dashTopicsPostRequest: DashTopicsPostRequest;
}

export interface DashTopicsTopicIdClienttopicsGetRequest {
    topicId: number;
    fields?: Array<DashTopicsTopicIdClienttopicsGetFieldsEnum>;
    page?: number;
    pageSize?: number;
}

export interface DashTopicsTopicIdDeleteRequest {
    topicId: number;
}

export interface DashTopicsTopicIdGetRequest {
    topicId: number;
    fields?: Array<DashTopicsTopicIdGetFieldsEnum>;
}

export interface DashTopicsTopicIdPutRequest {
    topicId: number;
    dashTopicsPostRequest: DashTopicsPostRequest;
}

export interface DashTopicsTopicIdSuggestionsTagsGetRequest {
    topicId: number;
}

export interface DashTopicsTopicIdWallpapersGetRequest {
    topicId: number;
    page?: number;
    pageSize?: number;
}

export interface DashTopicsTopicIdWallpapersPostOperationRequest {
    topicId: number;
    dashTopicsTopicIdWallpapersPostRequest: DashTopicsTopicIdWallpapersPostRequest;
}

export interface DashTopicsTopicIdWallpapersWallpaperIdDeleteRequest {
    topicId: number;
    wallpaperId: number;
}

export interface DashTopicsTopicIdWallpapersWallpaperIdPutRequest {
    topicId: number;
    wallpaperId: number;
}

export interface DashWallpapersGetRequest {
    page?: number;
    pageSize?: number;
    sizedFor?: DashWallpapersGetSizedForEnum;
    inTopic?: boolean;
}

export interface DashWallpapersWallpaperIdSimilarGetRequest {
    wallpaperId: number;
}

export interface DashWallpapersWallpaperIdTopicsGetRequest {
    wallpaperId: number;
}

export interface PostUploadWallpapersOperationRequest {
    postUploadWallpapersRequest: PostUploadWallpapersRequest;
}

/**
 * 
 */
export class DashboardApi extends runtime.BaseAPI {

    /**
     */
    async dashAuthTokenPostRaw(requestParameters: DashAuthTokenPostRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<DashAuthTokenPost200Response>> {
        if (requestParameters['cFTurnstileResponse'] == null) {
            throw new runtime.RequiredError(
                'cFTurnstileResponse',
                'Required parameter "cFTurnstileResponse" was null or undefined when calling dashAuthTokenPost().'
            );
        }

        if (requestParameters['username'] == null) {
            throw new runtime.RequiredError(
                'username',
                'Required parameter "username" was null or undefined when calling dashAuthTokenPost().'
            );
        }

        if (requestParameters['password'] == null) {
            throw new runtime.RequiredError(
                'password',
                'Required parameter "password" was null or undefined when calling dashAuthTokenPost().'
            );
        }

        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        if (requestParameters['cFTurnstileResponse'] != null) {
            headerParameters['CF-Turnstile-Response'] = String(requestParameters['cFTurnstileResponse']);
        }

        const consumes: runtime.Consume[] = [
            { contentType: 'application/x-www-form-urlencoded' },
            { contentType: 'application/json' },
        ];
        // @ts-ignore: canConsumeForm may be unused
        const canConsumeForm = runtime.canConsumeForm(consumes);

        let formParams: { append(param: string, value: any): any };
        let useForm = false;
        if (useForm) {
            formParams = new FormData();
        } else {
            formParams = new URLSearchParams();
        }

        if (requestParameters['username'] != null) {
            formParams.append('username', requestParameters['username'] as any);
        }

        if (requestParameters['password'] != null) {
            formParams.append('password', requestParameters['password'] as any);
        }


        let urlPath = `/dash/auth/token`;

        const response = await this.request({
            path: urlPath,
            method: 'POST',
            headers: headerParameters,
            query: queryParameters,
            body: formParams,
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => DashAuthTokenPost200ResponseFromJSON(jsonValue));
    }

    /**
     */
    async dashAuthTokenPost(requestParameters: DashAuthTokenPostRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<DashAuthTokenPost200Response> {
        const response = await this.dashAuthTokenPostRaw(requestParameters, initOverrides);
        return await response.value();
    }

    /**
     */
    async dashAuthTokenRefreshGetRaw(initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<DashAuthTokenPost200Response>> {
        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};


        let urlPath = `/dash/auth/token/refresh`;

        const response = await this.request({
            path: urlPath,
            method: 'GET',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => DashAuthTokenPost200ResponseFromJSON(jsonValue));
    }

    /**
     */
    async dashAuthTokenRefreshGet(initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<DashAuthTokenPost200Response> {
        const response = await this.dashAuthTokenRefreshGetRaw(initOverrides);
        return await response.value();
    }

    /**
     */
    async dashAuthTokenRevokeGetRaw(initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<void>> {
        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};


        let urlPath = `/dash/auth/token/revoke`;

        const response = await this.request({
            path: urlPath,
            method: 'GET',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        return new runtime.VoidApiResponse(response);
    }

    /**
     */
    async dashAuthTokenRevokeGet(initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<void> {
        await this.dashAuthTokenRevokeGetRaw(initOverrides);
    }

    /**
     */
    async dashClientsGetRaw(requestParameters: DashClientsGetRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<DashClientsGet200Response>> {
        const queryParameters: any = {};

        if (requestParameters['fields'] != null) {
            queryParameters['fields'] = requestParameters['fields'];
        }

        if (requestParameters['page'] != null) {
            queryParameters['page'] = requestParameters['page'];
        }

        if (requestParameters['pageSize'] != null) {
            queryParameters['page_size'] = requestParameters['pageSize'];
        }

        const headerParameters: runtime.HTTPHeaders = {};

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("access_token", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }

        let urlPath = `/dash/clients`;

        const response = await this.request({
            path: urlPath,
            method: 'GET',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => DashClientsGet200ResponseFromJSON(jsonValue));
    }

    /**
     */
    async dashClientsGet(requestParameters: DashClientsGetRequest = {}, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<DashClientsGet200Response> {
        const response = await this.dashClientsGetRaw(requestParameters, initOverrides);
        return await response.value();
    }

    /**
     */
    async dashClienttopicsClienttopicIdDeleteRaw(requestParameters: DashClienttopicsClienttopicIdDeleteRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<void>> {
        if (requestParameters['clienttopicId'] == null) {
            throw new runtime.RequiredError(
                'clienttopicId',
                'Required parameter "clienttopicId" was null or undefined when calling dashClienttopicsClienttopicIdDelete().'
            );
        }

        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("access_token", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }

        let urlPath = `/dash/clienttopics/{clienttopic_id}`;
        urlPath = urlPath.replace(`{${"clienttopic_id"}}`, encodeURIComponent(String(requestParameters['clienttopicId'])));

        const response = await this.request({
            path: urlPath,
            method: 'DELETE',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        return new runtime.VoidApiResponse(response);
    }

    /**
     */
    async dashClienttopicsClienttopicIdDelete(requestParameters: DashClienttopicsClienttopicIdDeleteRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<void> {
        await this.dashClienttopicsClienttopicIdDeleteRaw(requestParameters, initOverrides);
    }

    /**
     */
    async dashClienttopicsClienttopicIdPutRaw(requestParameters: DashClienttopicsClienttopicIdPutOperationRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<DashClienttopicsClienttopicIdPut200Response>> {
        if (requestParameters['clienttopicId'] == null) {
            throw new runtime.RequiredError(
                'clienttopicId',
                'Required parameter "clienttopicId" was null or undefined when calling dashClienttopicsClienttopicIdPut().'
            );
        }

        if (requestParameters['dashClienttopicsClienttopicIdPutRequest'] == null) {
            throw new runtime.RequiredError(
                'dashClienttopicsClienttopicIdPutRequest',
                'Required parameter "dashClienttopicsClienttopicIdPutRequest" was null or undefined when calling dashClienttopicsClienttopicIdPut().'
            );
        }

        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        headerParameters['Content-Type'] = 'application/json';

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("access_token", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }

        let urlPath = `/dash/clienttopics/{clienttopic_id}`;
        urlPath = urlPath.replace(`{${"clienttopic_id"}}`, encodeURIComponent(String(requestParameters['clienttopicId'])));

        const response = await this.request({
            path: urlPath,
            method: 'PUT',
            headers: headerParameters,
            query: queryParameters,
            body: DashClienttopicsClienttopicIdPutRequestToJSON(requestParameters['dashClienttopicsClienttopicIdPutRequest']),
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => DashClienttopicsClienttopicIdPut200ResponseFromJSON(jsonValue));
    }

    /**
     */
    async dashClienttopicsClienttopicIdPut(requestParameters: DashClienttopicsClienttopicIdPutOperationRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<DashClienttopicsClienttopicIdPut200Response> {
        const response = await this.dashClienttopicsClienttopicIdPutRaw(requestParameters, initOverrides);
        return await response.value();
    }

    /**
     */
    async dashClienttopicsGetRaw(requestParameters: DashClienttopicsGetRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<DashTopicsTopicIdClienttopicsGet200Response>> {
        const queryParameters: any = {};

        if (requestParameters['page'] != null) {
            queryParameters['page'] = requestParameters['page'];
        }

        if (requestParameters['pageSize'] != null) {
            queryParameters['page_size'] = requestParameters['pageSize'];
        }

        if (requestParameters['fields'] != null) {
            queryParameters['fields'] = requestParameters['fields'];
        }

        if (requestParameters['clientId'] != null) {
            queryParameters['client_id'] = requestParameters['clientId'];
        }

        const headerParameters: runtime.HTTPHeaders = {};

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("access_token", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }

        let urlPath = `/dash/clienttopics`;

        const response = await this.request({
            path: urlPath,
            method: 'GET',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => DashTopicsTopicIdClienttopicsGet200ResponseFromJSON(jsonValue));
    }

    /**
     */
    async dashClienttopicsGet(requestParameters: DashClienttopicsGetRequest = {}, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<DashTopicsTopicIdClienttopicsGet200Response> {
        const response = await this.dashClienttopicsGetRaw(requestParameters, initOverrides);
        return await response.value();
    }

    /**
     */
    async dashSearchWallpapersGetRaw(requestParameters: DashSearchWallpapersGetRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<DashSearchWallpapersGet200Response>> {
        if (requestParameters['q'] == null) {
            throw new runtime.RequiredError(
                'q',
                'Required parameter "q" was null or undefined when calling dashSearchWallpapersGet().'
            );
        }

        const queryParameters: any = {};

        if (requestParameters['q'] != null) {
            queryParameters['q'] = requestParameters['q'];
        }

        if (requestParameters['after'] != null) {
            queryParameters['after'] = requestParameters['after'];
        }

        if (requestParameters['limit'] != null) {
            queryParameters['limit'] = requestParameters['limit'];
        }

        if (requestParameters['size'] != null) {
            queryParameters['size'] = requestParameters['size'];
        }

        if (requestParameters['excludeTopicId'] != null) {
            queryParameters['exclude_topic_id'] = requestParameters['excludeTopicId'];
        }

        const headerParameters: runtime.HTTPHeaders = {};

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("access_token", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }

        let urlPath = `/dash/search/wallpapers`;

        const response = await this.request({
            path: urlPath,
            method: 'GET',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => DashSearchWallpapersGet200ResponseFromJSON(jsonValue));
    }

    /**
     */
    async dashSearchWallpapersGet(requestParameters: DashSearchWallpapersGetRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<DashSearchWallpapersGet200Response> {
        const response = await this.dashSearchWallpapersGetRaw(requestParameters, initOverrides);
        return await response.value();
    }

    /**
     */
    async dashSearchWallpapersV2GetRaw(requestParameters: DashSearchWallpapersV2GetRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<DashSearchWallpapersV2Get200Response>> {
        if (requestParameters['q'] == null) {
            throw new runtime.RequiredError(
                'q',
                'Required parameter "q" was null or undefined when calling dashSearchWallpapersV2Get().'
            );
        }

        const queryParameters: any = {};

        if (requestParameters['q'] != null) {
            queryParameters['q'] = requestParameters['q'];
        }

        if (requestParameters['sizedFor'] != null) {
            queryParameters['sized_for'] = requestParameters['sizedFor'];
        }

        const headerParameters: runtime.HTTPHeaders = {};

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("access_token", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }

        let urlPath = `/dash/search/wallpapers/v2`;

        const response = await this.request({
            path: urlPath,
            method: 'GET',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => DashSearchWallpapersV2Get200ResponseFromJSON(jsonValue));
    }

    /**
     */
    async dashSearchWallpapersV2Get(requestParameters: DashSearchWallpapersV2GetRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<DashSearchWallpapersV2Get200Response> {
        const response = await this.dashSearchWallpapersV2GetRaw(requestParameters, initOverrides);
        return await response.value();
    }

    /**
     */
    async dashStatsGetRaw(initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<DashStatsGet200Response>> {
        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("access_token", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }

        let urlPath = `/dash/stats`;

        const response = await this.request({
            path: urlPath,
            method: 'GET',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => DashStatsGet200ResponseFromJSON(jsonValue));
    }

    /**
     */
    async dashStatsGet(initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<DashStatsGet200Response> {
        const response = await this.dashStatsGetRaw(initOverrides);
        return await response.value();
    }

    /**
     * 发布专题
     */
    async dashTopicTopicIdPublishClientIdPostRaw(requestParameters: DashTopicTopicIdPublishClientIdPostOperationRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<void>> {
        if (requestParameters['topicId'] == null) {
            throw new runtime.RequiredError(
                'topicId',
                'Required parameter "topicId" was null or undefined when calling dashTopicTopicIdPublishClientIdPost().'
            );
        }

        if (requestParameters['clientId'] == null) {
            throw new runtime.RequiredError(
                'clientId',
                'Required parameter "clientId" was null or undefined when calling dashTopicTopicIdPublishClientIdPost().'
            );
        }

        if (requestParameters['dashTopicTopicIdPublishClientIdPostRequest'] == null) {
            throw new runtime.RequiredError(
                'dashTopicTopicIdPublishClientIdPostRequest',
                'Required parameter "dashTopicTopicIdPublishClientIdPostRequest" was null or undefined when calling dashTopicTopicIdPublishClientIdPost().'
            );
        }

        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        headerParameters['Content-Type'] = 'application/json';

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("access_token", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }

        let urlPath = `/dash/topic/{topic_id}/publish/{client_id}`;
        urlPath = urlPath.replace(`{${"topic_id"}}`, encodeURIComponent(String(requestParameters['topicId'])));
        urlPath = urlPath.replace(`{${"client_id"}}`, encodeURIComponent(String(requestParameters['clientId'])));

        const response = await this.request({
            path: urlPath,
            method: 'POST',
            headers: headerParameters,
            query: queryParameters,
            body: DashTopicTopicIdPublishClientIdPostRequestToJSON(requestParameters['dashTopicTopicIdPublishClientIdPostRequest']),
        }, initOverrides);

        return new runtime.VoidApiResponse(response);
    }

    /**
     * 发布专题
     */
    async dashTopicTopicIdPublishClientIdPost(requestParameters: DashTopicTopicIdPublishClientIdPostOperationRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<void> {
        await this.dashTopicTopicIdPublishClientIdPostRaw(requestParameters, initOverrides);
    }

    /**
     */
    async dashTopicsGetRaw(requestParameters: DashTopicsGetRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<DashTopicsGet200Response>> {
        const queryParameters: any = {};

        if (requestParameters['page'] != null) {
            queryParameters['page'] = requestParameters['page'];
        }

        if (requestParameters['pageSize'] != null) {
            queryParameters['page_size'] = requestParameters['pageSize'];
        }

        if (requestParameters['excludeWallpaperId'] != null) {
            queryParameters['exclude_wallpaper_id'] = requestParameters['excludeWallpaperId'];
        }

        if (requestParameters['inClient'] != null) {
            queryParameters['in_client'] = requestParameters['inClient'];
        }

        const headerParameters: runtime.HTTPHeaders = {};

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("access_token", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }

        let urlPath = `/dash/topics`;

        const response = await this.request({
            path: urlPath,
            method: 'GET',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => DashTopicsGet200ResponseFromJSON(jsonValue));
    }

    /**
     */
    async dashTopicsGet(requestParameters: DashTopicsGetRequest = {}, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<DashTopicsGet200Response> {
        const response = await this.dashTopicsGetRaw(requestParameters, initOverrides);
        return await response.value();
    }

    /**
     */
    async dashTopicsPostRaw(requestParameters: DashTopicsPostOperationRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<DashTopicsGet200ResponseResultsInner>> {
        if (requestParameters['dashTopicsPostRequest'] == null) {
            throw new runtime.RequiredError(
                'dashTopicsPostRequest',
                'Required parameter "dashTopicsPostRequest" was null or undefined when calling dashTopicsPost().'
            );
        }

        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        headerParameters['Content-Type'] = 'application/json';

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("access_token", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }

        let urlPath = `/dash/topics`;

        const response = await this.request({
            path: urlPath,
            method: 'POST',
            headers: headerParameters,
            query: queryParameters,
            body: DashTopicsPostRequestToJSON(requestParameters['dashTopicsPostRequest']),
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => DashTopicsGet200ResponseResultsInnerFromJSON(jsonValue));
    }

    /**
     */
    async dashTopicsPost(requestParameters: DashTopicsPostOperationRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<DashTopicsGet200ResponseResultsInner> {
        const response = await this.dashTopicsPostRaw(requestParameters, initOverrides);
        return await response.value();
    }

    /**
     */
    async dashTopicsTopicIdClienttopicsGetRaw(requestParameters: DashTopicsTopicIdClienttopicsGetRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<DashTopicsTopicIdClienttopicsGet200Response>> {
        if (requestParameters['topicId'] == null) {
            throw new runtime.RequiredError(
                'topicId',
                'Required parameter "topicId" was null or undefined when calling dashTopicsTopicIdClienttopicsGet().'
            );
        }

        const queryParameters: any = {};

        if (requestParameters['fields'] != null) {
            queryParameters['fields'] = requestParameters['fields'];
        }

        if (requestParameters['page'] != null) {
            queryParameters['page'] = requestParameters['page'];
        }

        if (requestParameters['pageSize'] != null) {
            queryParameters['page_size'] = requestParameters['pageSize'];
        }

        const headerParameters: runtime.HTTPHeaders = {};

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("access_token", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }

        let urlPath = `/dash/topics/{topic_id}/clienttopics`;
        urlPath = urlPath.replace(`{${"topic_id"}}`, encodeURIComponent(String(requestParameters['topicId'])));

        const response = await this.request({
            path: urlPath,
            method: 'GET',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => DashTopicsTopicIdClienttopicsGet200ResponseFromJSON(jsonValue));
    }

    /**
     */
    async dashTopicsTopicIdClienttopicsGet(requestParameters: DashTopicsTopicIdClienttopicsGetRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<DashTopicsTopicIdClienttopicsGet200Response> {
        const response = await this.dashTopicsTopicIdClienttopicsGetRaw(requestParameters, initOverrides);
        return await response.value();
    }

    /**
     */
    async dashTopicsTopicIdDeleteRaw(requestParameters: DashTopicsTopicIdDeleteRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<void>> {
        if (requestParameters['topicId'] == null) {
            throw new runtime.RequiredError(
                'topicId',
                'Required parameter "topicId" was null or undefined when calling dashTopicsTopicIdDelete().'
            );
        }

        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("access_token", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }

        let urlPath = `/dash/topics/{topic_id}`;
        urlPath = urlPath.replace(`{${"topic_id"}}`, encodeURIComponent(String(requestParameters['topicId'])));

        const response = await this.request({
            path: urlPath,
            method: 'DELETE',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        return new runtime.VoidApiResponse(response);
    }

    /**
     */
    async dashTopicsTopicIdDelete(requestParameters: DashTopicsTopicIdDeleteRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<void> {
        await this.dashTopicsTopicIdDeleteRaw(requestParameters, initOverrides);
    }

    /**
     */
    async dashTopicsTopicIdGetRaw(requestParameters: DashTopicsTopicIdGetRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<DashTopicsTopicIdGet200Response>> {
        if (requestParameters['topicId'] == null) {
            throw new runtime.RequiredError(
                'topicId',
                'Required parameter "topicId" was null or undefined when calling dashTopicsTopicIdGet().'
            );
        }

        const queryParameters: any = {};

        if (requestParameters['fields'] != null) {
            queryParameters['fields'] = requestParameters['fields'];
        }

        const headerParameters: runtime.HTTPHeaders = {};

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("access_token", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }

        let urlPath = `/dash/topics/{topic_id}`;
        urlPath = urlPath.replace(`{${"topic_id"}}`, encodeURIComponent(String(requestParameters['topicId'])));

        const response = await this.request({
            path: urlPath,
            method: 'GET',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => DashTopicsTopicIdGet200ResponseFromJSON(jsonValue));
    }

    /**
     */
    async dashTopicsTopicIdGet(requestParameters: DashTopicsTopicIdGetRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<DashTopicsTopicIdGet200Response> {
        const response = await this.dashTopicsTopicIdGetRaw(requestParameters, initOverrides);
        return await response.value();
    }

    /**
     */
    async dashTopicsTopicIdPutRaw(requestParameters: DashTopicsTopicIdPutRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<DashTopicsGet200ResponseResultsInner>> {
        if (requestParameters['topicId'] == null) {
            throw new runtime.RequiredError(
                'topicId',
                'Required parameter "topicId" was null or undefined when calling dashTopicsTopicIdPut().'
            );
        }

        if (requestParameters['dashTopicsPostRequest'] == null) {
            throw new runtime.RequiredError(
                'dashTopicsPostRequest',
                'Required parameter "dashTopicsPostRequest" was null or undefined when calling dashTopicsTopicIdPut().'
            );
        }

        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        headerParameters['Content-Type'] = 'application/json';

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("access_token", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }

        let urlPath = `/dash/topics/{topic_id}`;
        urlPath = urlPath.replace(`{${"topic_id"}}`, encodeURIComponent(String(requestParameters['topicId'])));

        const response = await this.request({
            path: urlPath,
            method: 'PUT',
            headers: headerParameters,
            query: queryParameters,
            body: DashTopicsPostRequestToJSON(requestParameters['dashTopicsPostRequest']),
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => DashTopicsGet200ResponseResultsInnerFromJSON(jsonValue));
    }

    /**
     */
    async dashTopicsTopicIdPut(requestParameters: DashTopicsTopicIdPutRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<DashTopicsGet200ResponseResultsInner> {
        const response = await this.dashTopicsTopicIdPutRaw(requestParameters, initOverrides);
        return await response.value();
    }

    /**
     * 获取 Topic 可能的标签
     */
    async dashTopicsTopicIdSuggestionsTagsGetRaw(requestParameters: DashTopicsTopicIdSuggestionsTagsGetRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<DashTopicsTopicIdSuggestionsTagsGet200Response>> {
        if (requestParameters['topicId'] == null) {
            throw new runtime.RequiredError(
                'topicId',
                'Required parameter "topicId" was null or undefined when calling dashTopicsTopicIdSuggestionsTagsGet().'
            );
        }

        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("access_token", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }

        let urlPath = `/dash/topics/{topic_id}/suggestions/tags`;
        urlPath = urlPath.replace(`{${"topic_id"}}`, encodeURIComponent(String(requestParameters['topicId'])));

        const response = await this.request({
            path: urlPath,
            method: 'GET',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => DashTopicsTopicIdSuggestionsTagsGet200ResponseFromJSON(jsonValue));
    }

    /**
     * 获取 Topic 可能的标签
     */
    async dashTopicsTopicIdSuggestionsTagsGet(requestParameters: DashTopicsTopicIdSuggestionsTagsGetRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<DashTopicsTopicIdSuggestionsTagsGet200Response> {
        const response = await this.dashTopicsTopicIdSuggestionsTagsGetRaw(requestParameters, initOverrides);
        return await response.value();
    }

    /**
     */
    async dashTopicsTopicIdWallpapersGetRaw(requestParameters: DashTopicsTopicIdWallpapersGetRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<DashTopicsTopicIdWallpapersGet200Response>> {
        if (requestParameters['topicId'] == null) {
            throw new runtime.RequiredError(
                'topicId',
                'Required parameter "topicId" was null or undefined when calling dashTopicsTopicIdWallpapersGet().'
            );
        }

        const queryParameters: any = {};

        if (requestParameters['page'] != null) {
            queryParameters['page'] = requestParameters['page'];
        }

        if (requestParameters['pageSize'] != null) {
            queryParameters['page_size'] = requestParameters['pageSize'];
        }

        const headerParameters: runtime.HTTPHeaders = {};

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("access_token", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }

        let urlPath = `/dash/topics/{topic_id}/wallpapers`;
        urlPath = urlPath.replace(`{${"topic_id"}}`, encodeURIComponent(String(requestParameters['topicId'])));

        const response = await this.request({
            path: urlPath,
            method: 'GET',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => DashTopicsTopicIdWallpapersGet200ResponseFromJSON(jsonValue));
    }

    /**
     */
    async dashTopicsTopicIdWallpapersGet(requestParameters: DashTopicsTopicIdWallpapersGetRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<DashTopicsTopicIdWallpapersGet200Response> {
        const response = await this.dashTopicsTopicIdWallpapersGetRaw(requestParameters, initOverrides);
        return await response.value();
    }

    /**
     */
    async dashTopicsTopicIdWallpapersPostRaw(requestParameters: DashTopicsTopicIdWallpapersPostOperationRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<void>> {
        if (requestParameters['topicId'] == null) {
            throw new runtime.RequiredError(
                'topicId',
                'Required parameter "topicId" was null or undefined when calling dashTopicsTopicIdWallpapersPost().'
            );
        }

        if (requestParameters['dashTopicsTopicIdWallpapersPostRequest'] == null) {
            throw new runtime.RequiredError(
                'dashTopicsTopicIdWallpapersPostRequest',
                'Required parameter "dashTopicsTopicIdWallpapersPostRequest" was null or undefined when calling dashTopicsTopicIdWallpapersPost().'
            );
        }

        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        headerParameters['Content-Type'] = 'application/json';

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("access_token", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }

        let urlPath = `/dash/topics/{topic_id}/wallpapers`;
        urlPath = urlPath.replace(`{${"topic_id"}}`, encodeURIComponent(String(requestParameters['topicId'])));

        const response = await this.request({
            path: urlPath,
            method: 'POST',
            headers: headerParameters,
            query: queryParameters,
            body: DashTopicsTopicIdWallpapersPostRequestToJSON(requestParameters['dashTopicsTopicIdWallpapersPostRequest']),
        }, initOverrides);

        return new runtime.VoidApiResponse(response);
    }

    /**
     */
    async dashTopicsTopicIdWallpapersPost(requestParameters: DashTopicsTopicIdWallpapersPostOperationRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<void> {
        await this.dashTopicsTopicIdWallpapersPostRaw(requestParameters, initOverrides);
    }

    /**
     * 取消壁纸与话题的关联
     */
    async dashTopicsTopicIdWallpapersWallpaperIdDeleteRaw(requestParameters: DashTopicsTopicIdWallpapersWallpaperIdDeleteRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<void>> {
        if (requestParameters['topicId'] == null) {
            throw new runtime.RequiredError(
                'topicId',
                'Required parameter "topicId" was null or undefined when calling dashTopicsTopicIdWallpapersWallpaperIdDelete().'
            );
        }

        if (requestParameters['wallpaperId'] == null) {
            throw new runtime.RequiredError(
                'wallpaperId',
                'Required parameter "wallpaperId" was null or undefined when calling dashTopicsTopicIdWallpapersWallpaperIdDelete().'
            );
        }

        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("access_token", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }

        let urlPath = `/dash/topics/{topic_id}/wallpapers/{wallpaper_id}`;
        urlPath = urlPath.replace(`{${"topic_id"}}`, encodeURIComponent(String(requestParameters['topicId'])));
        urlPath = urlPath.replace(`{${"wallpaper_id"}}`, encodeURIComponent(String(requestParameters['wallpaperId'])));

        const response = await this.request({
            path: urlPath,
            method: 'DELETE',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        return new runtime.VoidApiResponse(response);
    }

    /**
     * 取消壁纸与话题的关联
     */
    async dashTopicsTopicIdWallpapersWallpaperIdDelete(requestParameters: DashTopicsTopicIdWallpapersWallpaperIdDeleteRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<void> {
        await this.dashTopicsTopicIdWallpapersWallpaperIdDeleteRaw(requestParameters, initOverrides);
    }

    /**
     * 将壁纸与话题关联
     */
    async dashTopicsTopicIdWallpapersWallpaperIdPutRaw(requestParameters: DashTopicsTopicIdWallpapersWallpaperIdPutRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<void>> {
        if (requestParameters['topicId'] == null) {
            throw new runtime.RequiredError(
                'topicId',
                'Required parameter "topicId" was null or undefined when calling dashTopicsTopicIdWallpapersWallpaperIdPut().'
            );
        }

        if (requestParameters['wallpaperId'] == null) {
            throw new runtime.RequiredError(
                'wallpaperId',
                'Required parameter "wallpaperId" was null or undefined when calling dashTopicsTopicIdWallpapersWallpaperIdPut().'
            );
        }

        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("access_token", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }

        let urlPath = `/dash/topics/{topic_id}/wallpapers/{wallpaper_id}`;
        urlPath = urlPath.replace(`{${"topic_id"}}`, encodeURIComponent(String(requestParameters['topicId'])));
        urlPath = urlPath.replace(`{${"wallpaper_id"}}`, encodeURIComponent(String(requestParameters['wallpaperId'])));

        const response = await this.request({
            path: urlPath,
            method: 'PUT',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        return new runtime.VoidApiResponse(response);
    }

    /**
     * 将壁纸与话题关联
     */
    async dashTopicsTopicIdWallpapersWallpaperIdPut(requestParameters: DashTopicsTopicIdWallpapersWallpaperIdPutRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<void> {
        await this.dashTopicsTopicIdWallpapersWallpaperIdPutRaw(requestParameters, initOverrides);
    }

    /**
     */
    async dashUserinfoGetRaw(initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<DashUserinfoGet200Response>> {
        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("access_token", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }

        let urlPath = `/dash/userinfo`;

        const response = await this.request({
            path: urlPath,
            method: 'GET',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => DashUserinfoGet200ResponseFromJSON(jsonValue));
    }

    /**
     */
    async dashUserinfoGet(initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<DashUserinfoGet200Response> {
        const response = await this.dashUserinfoGetRaw(initOverrides);
        return await response.value();
    }

    /**
     */
    async dashWallpapersGetRaw(requestParameters: DashWallpapersGetRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<DashTopicsTopicIdWallpapersGet200Response>> {
        const queryParameters: any = {};

        if (requestParameters['page'] != null) {
            queryParameters['page'] = requestParameters['page'];
        }

        if (requestParameters['pageSize'] != null) {
            queryParameters['page_size'] = requestParameters['pageSize'];
        }

        if (requestParameters['sizedFor'] != null) {
            queryParameters['sized_for'] = requestParameters['sizedFor'];
        }

        if (requestParameters['inTopic'] != null) {
            queryParameters['in_topic'] = requestParameters['inTopic'];
        }

        const headerParameters: runtime.HTTPHeaders = {};

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("access_token", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }

        let urlPath = `/dash/wallpapers`;

        const response = await this.request({
            path: urlPath,
            method: 'GET',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => DashTopicsTopicIdWallpapersGet200ResponseFromJSON(jsonValue));
    }

    /**
     */
    async dashWallpapersGet(requestParameters: DashWallpapersGetRequest = {}, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<DashTopicsTopicIdWallpapersGet200Response> {
        const response = await this.dashWallpapersGetRaw(requestParameters, initOverrides);
        return await response.value();
    }

    /**
     * @deprecated
     */
    async dashWallpapersWallpaperIdSimilarGetRaw(requestParameters: DashWallpapersWallpaperIdSimilarGetRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<DashWallpapersWallpaperIdSimilarGet200Response>> {
        if (requestParameters['wallpaperId'] == null) {
            throw new runtime.RequiredError(
                'wallpaperId',
                'Required parameter "wallpaperId" was null or undefined when calling dashWallpapersWallpaperIdSimilarGet().'
            );
        }

        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("access_token", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }

        let urlPath = `/dash/wallpapers/{wallpaper_id}/similar`;
        urlPath = urlPath.replace(`{${"wallpaper_id"}}`, encodeURIComponent(String(requestParameters['wallpaperId'])));

        const response = await this.request({
            path: urlPath,
            method: 'GET',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => DashWallpapersWallpaperIdSimilarGet200ResponseFromJSON(jsonValue));
    }

    /**
     * @deprecated
     */
    async dashWallpapersWallpaperIdSimilarGet(requestParameters: DashWallpapersWallpaperIdSimilarGetRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<DashWallpapersWallpaperIdSimilarGet200Response> {
        const response = await this.dashWallpapersWallpaperIdSimilarGetRaw(requestParameters, initOverrides);
        return await response.value();
    }

    /**
     */
    async dashWallpapersWallpaperIdTopicsGetRaw(requestParameters: DashWallpapersWallpaperIdTopicsGetRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<DashWallpapersWallpaperIdTopicsGet200Response>> {
        if (requestParameters['wallpaperId'] == null) {
            throw new runtime.RequiredError(
                'wallpaperId',
                'Required parameter "wallpaperId" was null or undefined when calling dashWallpapersWallpaperIdTopicsGet().'
            );
        }

        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("access_token", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }

        let urlPath = `/dash/wallpapers/{wallpaper_id}/topics`;
        urlPath = urlPath.replace(`{${"wallpaper_id"}}`, encodeURIComponent(String(requestParameters['wallpaperId'])));

        const response = await this.request({
            path: urlPath,
            method: 'GET',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => DashWallpapersWallpaperIdTopicsGet200ResponseFromJSON(jsonValue));
    }

    /**
     */
    async dashWallpapersWallpaperIdTopicsGet(requestParameters: DashWallpapersWallpaperIdTopicsGetRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<DashWallpapersWallpaperIdTopicsGet200Response> {
        const response = await this.dashWallpapersWallpaperIdTopicsGetRaw(requestParameters, initOverrides);
        return await response.value();
    }

    /**
     */
    async postUploadWallpapersRaw(requestParameters: PostUploadWallpapersOperationRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<void>> {
        if (requestParameters['postUploadWallpapersRequest'] == null) {
            throw new runtime.RequiredError(
                'postUploadWallpapersRequest',
                'Required parameter "postUploadWallpapersRequest" was null or undefined when calling postUploadWallpapers().'
            );
        }

        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        headerParameters['Content-Type'] = 'application/json';

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("access_token", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }

        let urlPath = `/dash/uploadwallpapers`;

        const response = await this.request({
            path: urlPath,
            method: 'POST',
            headers: headerParameters,
            query: queryParameters,
            body: PostUploadWallpapersRequestToJSON(requestParameters['postUploadWallpapersRequest']),
        }, initOverrides);

        return new runtime.VoidApiResponse(response);
    }

    /**
     */
    async postUploadWallpapers(requestParameters: PostUploadWallpapersOperationRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<void> {
        await this.postUploadWallpapersRaw(requestParameters, initOverrides);
    }

}

/**
 * @export
 */
export const DashClientsGetFieldsEnum = {
    Page: 'page',
    PageSize: 'page_size',
    Total: 'total',
    ResultsId: 'results.id',
    ResultsName: 'results.name'
} as const;
export type DashClientsGetFieldsEnum = typeof DashClientsGetFieldsEnum[keyof typeof DashClientsGetFieldsEnum];
/**
 * @export
 */
export const DashClienttopicsGetFieldsEnum = {
    Page: 'page',
    PageSize: 'page_size',
    Total: 'total',
    ResultsId: 'results.id',
    ResultsTitle: 'results.title',
    ResultsTopicId: 'results.topic.id',
    ResultsTopicComment: 'results.topic.comment',
    ResultsClientId: 'results.client.id',
    ResultsClientName: 'results.client.name',
    ResultsPublishedAt: 'results.published_at'
} as const;
export type DashClienttopicsGetFieldsEnum = typeof DashClienttopicsGetFieldsEnum[keyof typeof DashClienttopicsGetFieldsEnum];
/**
 * @export
 */
export const DashSearchWallpapersV2GetSizedForEnum = {
    Desktop: 'desktop',
    Mobile: 'mobile'
} as const;
export type DashSearchWallpapersV2GetSizedForEnum = typeof DashSearchWallpapersV2GetSizedForEnum[keyof typeof DashSearchWallpapersV2GetSizedForEnum];
/**
 * @export
 */
export const DashTopicsTopicIdClienttopicsGetFieldsEnum = {
    Page: 'page',
    PageSize: 'page_size',
    Total: 'total',
    ResultsId: 'results.id',
    ResultsTitle: 'results.title',
    ResultsTopicId: 'results.topic.id',
    ResultsTopicComment: 'results.topic.comment',
    ResultsClientId: 'results.client.id',
    ResultsClientName: 'results.client.name',
    ResultsPublishedAt: 'results.published_at'
} as const;
export type DashTopicsTopicIdClienttopicsGetFieldsEnum = typeof DashTopicsTopicIdClienttopicsGetFieldsEnum[keyof typeof DashTopicsTopicIdClienttopicsGetFieldsEnum];
/**
 * @export
 */
export const DashTopicsTopicIdGetFieldsEnum = {
    Id: 'id',
    Comment: 'comment'
} as const;
export type DashTopicsTopicIdGetFieldsEnum = typeof DashTopicsTopicIdGetFieldsEnum[keyof typeof DashTopicsTopicIdGetFieldsEnum];
/**
 * @export
 */
export const DashWallpapersGetSizedForEnum = {
    Desktop: 'desktop',
    Mobile: 'mobile'
} as const;
export type DashWallpapersGetSizedForEnum = typeof DashWallpapersGetSizedForEnum[keyof typeof DashWallpapersGetSizedForEnum];
