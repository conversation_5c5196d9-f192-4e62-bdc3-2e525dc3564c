/* tslint:disable */
/* eslint-disable */
/**
 * Wallpapers API
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 0.1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import * as runtime from '../runtime';
import type {
  RestClientsClientIdClienttopicsGet200Response,
  RestWallpapersGet200Response,
  RestWallpapersWallpaperIdSimilarGet200Response,
  RestWallpapersWallpaperIdUploadersGet200Response,
} from '../models/index';
import {
    RestClientsClientIdClienttopicsGet200ResponseFromJSON,
    RestClientsClientIdClienttopicsGet200ResponseToJSON,
    RestWallpapersGet200ResponseFromJSON,
    RestWallpapersGet200ResponseToJSON,
    RestWallpapersWallpaperIdSimilarGet200ResponseFromJSON,
    RestWallpapersWallpaperIdSimilarGet200ResponseToJSON,
    RestWallpapersWallpaperIdUploadersGet200ResponseFromJSON,
    RestWallpapersWallpaperIdUploadersGet200ResponseToJSON,
} from '../models/index';

export interface RestClientsClientIdClienttopicsGetRequest {
    clientId: string;
    page?: number;
    pageSize?: number;
    include?: Array<RestClientsClientIdClienttopicsGetIncludeEnum>;
}

export interface RestTopicsTopicIdWallpapersGetRequest {
    topicId: number;
    page?: number;
    pageSize?: number;
    size?: Array<string>;
}

export interface RestWallpapersGetRequest {
    page?: number;
    pageSize?: number;
    size?: Array<string>;
    aspectRatio?: Array<string>;
    inTopic?: boolean;
}

export interface RestWallpapersWallpaperIdSimilarGetRequest {
    wallpaperId: number;
    size?: Array<string>;
    limit?: number;
    offset?: number;
    scoreThreshold?: number;
    aspectRatio?: Array<string>;
}

export interface RestWallpapersWallpaperIdUploadersGetRequest {
    wallpaperId: number;
    page?: number;
    pageSize?: number;
}

/**
 * 
 */
export class RestApi extends runtime.BaseAPI {

    /**
     */
    async restClientsClientIdClienttopicsGetRaw(requestParameters: RestClientsClientIdClienttopicsGetRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<RestClientsClientIdClienttopicsGet200Response>> {
        if (requestParameters['clientId'] == null) {
            throw new runtime.RequiredError(
                'clientId',
                'Required parameter "clientId" was null or undefined when calling restClientsClientIdClienttopicsGet().'
            );
        }

        const queryParameters: any = {};

        if (requestParameters['page'] != null) {
            queryParameters['page'] = requestParameters['page'];
        }

        if (requestParameters['pageSize'] != null) {
            queryParameters['page_size'] = requestParameters['pageSize'];
        }

        if (requestParameters['include'] != null) {
            queryParameters['include'] = requestParameters['include'];
        }

        const headerParameters: runtime.HTTPHeaders = {};


        let urlPath = `/rest/clients/{client_id}/clienttopics`;
        urlPath = urlPath.replace(`{${"client_id"}}`, encodeURIComponent(String(requestParameters['clientId'])));

        const response = await this.request({
            path: urlPath,
            method: 'GET',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => RestClientsClientIdClienttopicsGet200ResponseFromJSON(jsonValue));
    }

    /**
     */
    async restClientsClientIdClienttopicsGet(requestParameters: RestClientsClientIdClienttopicsGetRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<RestClientsClientIdClienttopicsGet200Response> {
        const response = await this.restClientsClientIdClienttopicsGetRaw(requestParameters, initOverrides);
        return await response.value();
    }

    /**
     */
    async restTopicsTopicIdWallpapersGetRaw(requestParameters: RestTopicsTopicIdWallpapersGetRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<RestWallpapersGet200Response>> {
        if (requestParameters['topicId'] == null) {
            throw new runtime.RequiredError(
                'topicId',
                'Required parameter "topicId" was null or undefined when calling restTopicsTopicIdWallpapersGet().'
            );
        }

        const queryParameters: any = {};

        if (requestParameters['page'] != null) {
            queryParameters['page'] = requestParameters['page'];
        }

        if (requestParameters['pageSize'] != null) {
            queryParameters['page_size'] = requestParameters['pageSize'];
        }

        if (requestParameters['size'] != null) {
            queryParameters['size'] = requestParameters['size'];
        }

        const headerParameters: runtime.HTTPHeaders = {};

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("access_token", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }

        let urlPath = `/rest/topics/{topic_id}/wallpapers`;
        urlPath = urlPath.replace(`{${"topic_id"}}`, encodeURIComponent(String(requestParameters['topicId'])));

        const response = await this.request({
            path: urlPath,
            method: 'GET',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => RestWallpapersGet200ResponseFromJSON(jsonValue));
    }

    /**
     */
    async restTopicsTopicIdWallpapersGet(requestParameters: RestTopicsTopicIdWallpapersGetRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<RestWallpapersGet200Response> {
        const response = await this.restTopicsTopicIdWallpapersGetRaw(requestParameters, initOverrides);
        return await response.value();
    }

    /**
     */
    async restWallpapersGetRaw(requestParameters: RestWallpapersGetRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<RestWallpapersGet200Response>> {
        const queryParameters: any = {};

        if (requestParameters['page'] != null) {
            queryParameters['page'] = requestParameters['page'];
        }

        if (requestParameters['pageSize'] != null) {
            queryParameters['page_size'] = requestParameters['pageSize'];
        }

        if (requestParameters['size'] != null) {
            queryParameters['size'] = requestParameters['size'];
        }

        if (requestParameters['aspectRatio'] != null) {
            queryParameters['aspect_ratio'] = requestParameters['aspectRatio'];
        }

        if (requestParameters['inTopic'] != null) {
            queryParameters['in_topic'] = requestParameters['inTopic'];
        }

        const headerParameters: runtime.HTTPHeaders = {};

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("access_token", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }

        let urlPath = `/rest/wallpapers`;

        const response = await this.request({
            path: urlPath,
            method: 'GET',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => RestWallpapersGet200ResponseFromJSON(jsonValue));
    }

    /**
     */
    async restWallpapersGet(requestParameters: RestWallpapersGetRequest = {}, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<RestWallpapersGet200Response> {
        const response = await this.restWallpapersGetRaw(requestParameters, initOverrides);
        return await response.value();
    }

    /**
     */
    async restWallpapersWallpaperIdSimilarGetRaw(requestParameters: RestWallpapersWallpaperIdSimilarGetRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<RestWallpapersWallpaperIdSimilarGet200Response>> {
        if (requestParameters['wallpaperId'] == null) {
            throw new runtime.RequiredError(
                'wallpaperId',
                'Required parameter "wallpaperId" was null or undefined when calling restWallpapersWallpaperIdSimilarGet().'
            );
        }

        const queryParameters: any = {};

        if (requestParameters['size'] != null) {
            queryParameters['size'] = requestParameters['size'];
        }

        if (requestParameters['limit'] != null) {
            queryParameters['limit'] = requestParameters['limit'];
        }

        if (requestParameters['offset'] != null) {
            queryParameters['offset'] = requestParameters['offset'];
        }

        if (requestParameters['scoreThreshold'] != null) {
            queryParameters['score_threshold'] = requestParameters['scoreThreshold'];
        }

        if (requestParameters['aspectRatio'] != null) {
            queryParameters['aspect_ratio'] = requestParameters['aspectRatio'];
        }

        const headerParameters: runtime.HTTPHeaders = {};

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("access_token", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }

        let urlPath = `/rest/wallpapers/{wallpaper_id}/similar`;
        urlPath = urlPath.replace(`{${"wallpaper_id"}}`, encodeURIComponent(String(requestParameters['wallpaperId'])));

        const response = await this.request({
            path: urlPath,
            method: 'GET',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => RestWallpapersWallpaperIdSimilarGet200ResponseFromJSON(jsonValue));
    }

    /**
     */
    async restWallpapersWallpaperIdSimilarGet(requestParameters: RestWallpapersWallpaperIdSimilarGetRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<RestWallpapersWallpaperIdSimilarGet200Response> {
        const response = await this.restWallpapersWallpaperIdSimilarGetRaw(requestParameters, initOverrides);
        return await response.value();
    }

    /**
     */
    async restWallpapersWallpaperIdUploadersGetRaw(requestParameters: RestWallpapersWallpaperIdUploadersGetRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<RestWallpapersWallpaperIdUploadersGet200Response>> {
        if (requestParameters['wallpaperId'] == null) {
            throw new runtime.RequiredError(
                'wallpaperId',
                'Required parameter "wallpaperId" was null or undefined when calling restWallpapersWallpaperIdUploadersGet().'
            );
        }

        const queryParameters: any = {};

        if (requestParameters['page'] != null) {
            queryParameters['page'] = requestParameters['page'];
        }

        if (requestParameters['pageSize'] != null) {
            queryParameters['page_size'] = requestParameters['pageSize'];
        }

        const headerParameters: runtime.HTTPHeaders = {};


        let urlPath = `/rest/wallpapers/{wallpaper_id}/uploaders`;
        urlPath = urlPath.replace(`{${"wallpaper_id"}}`, encodeURIComponent(String(requestParameters['wallpaperId'])));

        const response = await this.request({
            path: urlPath,
            method: 'GET',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => RestWallpapersWallpaperIdUploadersGet200ResponseFromJSON(jsonValue));
    }

    /**
     */
    async restWallpapersWallpaperIdUploadersGet(requestParameters: RestWallpapersWallpaperIdUploadersGetRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<RestWallpapersWallpaperIdUploadersGet200Response> {
        const response = await this.restWallpapersWallpaperIdUploadersGetRaw(requestParameters, initOverrides);
        return await response.value();
    }

}

/**
 * @export
 */
export const RestClientsClientIdClienttopicsGetIncludeEnum = {
    Topic: 'topic'
} as const;
export type RestClientsClientIdClienttopicsGetIncludeEnum = typeof RestClientsClientIdClienttopicsGetIncludeEnum[keyof typeof RestClientsClientIdClienttopicsGetIncludeEnum];
