/* tslint:disable */
/* eslint-disable */
/**
 * Wallpapers API
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 0.1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import * as runtime from '../runtime';
import type {
  CategoriesColorsGet200Response,
  CategoriesTagsGet200Response,
  ClientsClientIdTopicsGet200Response,
  ClientsClientIdTopicsTopicIdWallpapersGet200Response,
  WallpapersGet200Response,
  WallpapersKeyRelatedGet200Response,
} from '../models/index';
import {
    CategoriesColorsGet200ResponseFromJSON,
    CategoriesColorsGet200ResponseToJSON,
    CategoriesTagsGet200ResponseFromJSON,
    CategoriesTagsGet200ResponseToJSON,
    ClientsClientIdTopicsGet200ResponseFromJSON,
    ClientsClientIdTopicsGet200ResponseToJSON,
    ClientsClientIdTopicsTopicIdWallpapersGet200ResponseFromJSON,
    ClientsClientIdTopicsTopicIdWallpapersGet200ResponseToJSON,
    WallpapersGet200ResponseFromJSON,
    WallpapersGet200ResponseToJSON,
    WallpapersKeyRelatedGet200ResponseFromJSON,
    WallpapersKeyRelatedGet200ResponseToJSON,
} from '../models/index';

export interface ClientsClientIdTopicsGetRequest {
    clientId: string;
    group?: string;
    after?: number;
    limit?: number;
}

export interface ClientsClientIdTopicsTopicIdWallpapersGetRequest {
    clientId: string;
    topicId: number;
    size?: Array<string>;
}

export interface ImageKeyGetRequest {
    key: string;
    cFTurnstileResponse: string;
}

export interface WallpapersGetRequest {
    size?: Array<string>;
    page?: number;
    pageSize?: number;
    sizedFor?: WallpapersGetSizedForEnum;
    color?: string;
    tag?: string;
}

export interface WallpapersKeyRelatedGetRequest {
    key: string;
    num?: number;
    size?: Array<string>;
}

/**
 * 
 */
export class DefaultApi extends runtime.BaseAPI {

    /**
     * @deprecated
     */
    async categoriesColorsGetRaw(initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<CategoriesColorsGet200Response>> {
        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};


        let urlPath = `/categories/colors`;

        const response = await this.request({
            path: urlPath,
            method: 'GET',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => CategoriesColorsGet200ResponseFromJSON(jsonValue));
    }

    /**
     * @deprecated
     */
    async categoriesColorsGet(initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<CategoriesColorsGet200Response> {
        const response = await this.categoriesColorsGetRaw(initOverrides);
        return await response.value();
    }

    /**
     * @deprecated
     */
    async categoriesTagsGetRaw(initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<CategoriesTagsGet200Response>> {
        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};


        let urlPath = `/categories/tags`;

        const response = await this.request({
            path: urlPath,
            method: 'GET',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => CategoriesTagsGet200ResponseFromJSON(jsonValue));
    }

    /**
     * @deprecated
     */
    async categoriesTagsGet(initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<CategoriesTagsGet200Response> {
        const response = await this.categoriesTagsGetRaw(initOverrides);
        return await response.value();
    }

    /**
     * @deprecated
     */
    async clientsClientIdTopicsGetRaw(requestParameters: ClientsClientIdTopicsGetRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<ClientsClientIdTopicsGet200Response>> {
        if (requestParameters['clientId'] == null) {
            throw new runtime.RequiredError(
                'clientId',
                'Required parameter "clientId" was null or undefined when calling clientsClientIdTopicsGet().'
            );
        }

        const queryParameters: any = {};

        if (requestParameters['group'] != null) {
            queryParameters['group'] = requestParameters['group'];
        }

        if (requestParameters['after'] != null) {
            queryParameters['after'] = requestParameters['after'];
        }

        if (requestParameters['limit'] != null) {
            queryParameters['limit'] = requestParameters['limit'];
        }

        const headerParameters: runtime.HTTPHeaders = {};


        let urlPath = `/clients/{client_id}/topics`;
        urlPath = urlPath.replace(`{${"client_id"}}`, encodeURIComponent(String(requestParameters['clientId'])));

        const response = await this.request({
            path: urlPath,
            method: 'GET',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => ClientsClientIdTopicsGet200ResponseFromJSON(jsonValue));
    }

    /**
     * @deprecated
     */
    async clientsClientIdTopicsGet(requestParameters: ClientsClientIdTopicsGetRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<ClientsClientIdTopicsGet200Response> {
        const response = await this.clientsClientIdTopicsGetRaw(requestParameters, initOverrides);
        return await response.value();
    }

    /**
     * @deprecated
     */
    async clientsClientIdTopicsTopicIdWallpapersGetRaw(requestParameters: ClientsClientIdTopicsTopicIdWallpapersGetRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<ClientsClientIdTopicsTopicIdWallpapersGet200Response>> {
        if (requestParameters['clientId'] == null) {
            throw new runtime.RequiredError(
                'clientId',
                'Required parameter "clientId" was null or undefined when calling clientsClientIdTopicsTopicIdWallpapersGet().'
            );
        }

        if (requestParameters['topicId'] == null) {
            throw new runtime.RequiredError(
                'topicId',
                'Required parameter "topicId" was null or undefined when calling clientsClientIdTopicsTopicIdWallpapersGet().'
            );
        }

        const queryParameters: any = {};

        if (requestParameters['size'] != null) {
            queryParameters['size'] = requestParameters['size'];
        }

        const headerParameters: runtime.HTTPHeaders = {};


        let urlPath = `/clients/{client_id}/topics/{topic_id}/wallpapers`;
        urlPath = urlPath.replace(`{${"client_id"}}`, encodeURIComponent(String(requestParameters['clientId'])));
        urlPath = urlPath.replace(`{${"topic_id"}}`, encodeURIComponent(String(requestParameters['topicId'])));

        const response = await this.request({
            path: urlPath,
            method: 'GET',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => ClientsClientIdTopicsTopicIdWallpapersGet200ResponseFromJSON(jsonValue));
    }

    /**
     * @deprecated
     */
    async clientsClientIdTopicsTopicIdWallpapersGet(requestParameters: ClientsClientIdTopicsTopicIdWallpapersGetRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<ClientsClientIdTopicsTopicIdWallpapersGet200Response> {
        const response = await this.clientsClientIdTopicsTopicIdWallpapersGetRaw(requestParameters, initOverrides);
        return await response.value();
    }

    /**
     * 获取原始图片
     */
    async imageKeyGetRaw(requestParameters: ImageKeyGetRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<void>> {
        if (requestParameters['key'] == null) {
            throw new runtime.RequiredError(
                'key',
                'Required parameter "key" was null or undefined when calling imageKeyGet().'
            );
        }

        if (requestParameters['cFTurnstileResponse'] == null) {
            throw new runtime.RequiredError(
                'cFTurnstileResponse',
                'Required parameter "cFTurnstileResponse" was null or undefined when calling imageKeyGet().'
            );
        }

        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        if (requestParameters['cFTurnstileResponse'] != null) {
            headerParameters['CF-Turnstile-Response'] = String(requestParameters['cFTurnstileResponse']);
        }


        let urlPath = `/image/{key}`;
        urlPath = urlPath.replace(`{${"key"}}`, encodeURIComponent(String(requestParameters['key'])));

        const response = await this.request({
            path: urlPath,
            method: 'GET',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        return new runtime.VoidApiResponse(response);
    }

    /**
     * 获取原始图片
     */
    async imageKeyGet(requestParameters: ImageKeyGetRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<void> {
        await this.imageKeyGetRaw(requestParameters, initOverrides);
    }

    /**
     * 图片为等比例缩放
     * 获取壁纸列表，不包含原始图片地址，所以作为预览展示使用。
     * @deprecated
     */
    async wallpapersGetRaw(requestParameters: WallpapersGetRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<WallpapersGet200Response>> {
        const queryParameters: any = {};

        if (requestParameters['size'] != null) {
            queryParameters['size'] = requestParameters['size'];
        }

        if (requestParameters['page'] != null) {
            queryParameters['page'] = requestParameters['page'];
        }

        if (requestParameters['pageSize'] != null) {
            queryParameters['page_size'] = requestParameters['pageSize'];
        }

        if (requestParameters['sizedFor'] != null) {
            queryParameters['sized_for'] = requestParameters['sizedFor'];
        }

        if (requestParameters['color'] != null) {
            queryParameters['color'] = requestParameters['color'];
        }

        if (requestParameters['tag'] != null) {
            queryParameters['tag'] = requestParameters['tag'];
        }

        const headerParameters: runtime.HTTPHeaders = {};


        let urlPath = `/wallpapers`;

        const response = await this.request({
            path: urlPath,
            method: 'GET',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => WallpapersGet200ResponseFromJSON(jsonValue));
    }

    /**
     * 图片为等比例缩放
     * 获取壁纸列表，不包含原始图片地址，所以作为预览展示使用。
     * @deprecated
     */
    async wallpapersGet(requestParameters: WallpapersGetRequest = {}, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<WallpapersGet200Response> {
        const response = await this.wallpapersGetRaw(requestParameters, initOverrides);
        return await response.value();
    }

    /**
     * 获取当前壁纸的相关其他壁纸 (固定数量的)
     * @deprecated
     */
    async wallpapersKeyRelatedGetRaw(requestParameters: WallpapersKeyRelatedGetRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<WallpapersKeyRelatedGet200Response>> {
        if (requestParameters['key'] == null) {
            throw new runtime.RequiredError(
                'key',
                'Required parameter "key" was null or undefined when calling wallpapersKeyRelatedGet().'
            );
        }

        const queryParameters: any = {};

        if (requestParameters['num'] != null) {
            queryParameters['num'] = requestParameters['num'];
        }

        if (requestParameters['size'] != null) {
            queryParameters['size'] = requestParameters['size'];
        }

        const headerParameters: runtime.HTTPHeaders = {};


        let urlPath = `/wallpapers/{key}/related`;
        urlPath = urlPath.replace(`{${"key"}}`, encodeURIComponent(String(requestParameters['key'])));

        const response = await this.request({
            path: urlPath,
            method: 'GET',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => WallpapersKeyRelatedGet200ResponseFromJSON(jsonValue));
    }

    /**
     * 获取当前壁纸的相关其他壁纸 (固定数量的)
     * @deprecated
     */
    async wallpapersKeyRelatedGet(requestParameters: WallpapersKeyRelatedGetRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<WallpapersKeyRelatedGet200Response> {
        const response = await this.wallpapersKeyRelatedGetRaw(requestParameters, initOverrides);
        return await response.value();
    }

}

/**
 * @export
 */
export const WallpapersGetSizedForEnum = {
    Desktop: 'desktop',
    Mobile: 'mobile'
} as const;
export type WallpapersGetSizedForEnum = typeof WallpapersGetSizedForEnum[keyof typeof WallpapersGetSizedForEnum];
