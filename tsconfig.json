{"extends": "astro/tsconfigs/strict", "include": [".astro/types.d.ts", "**/*"], "exclude": ["dist", "api-client"], "compilerOptions": {"jsx": "react-jsx", "jsxImportSource": "preact", "skipLibCheck": true, "baseUrl": "./", "paths": {"react": ["./node_modules/preact/compat/"], "react/jsx-runtime": ["./node_modules/preact/jsx-runtime"], "react-dom": ["./node_modules/preact/compat/"], "react-dom/*": ["./node_modules/preact/compat/*"]}}}