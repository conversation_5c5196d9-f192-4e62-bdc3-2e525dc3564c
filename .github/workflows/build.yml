name: Building

on:
  push:
    branches:
      - main
  schedule:
    # 每天北京时间凌晨2点执行 (UTC 18:00)
    - cron: '0 18 * * *'

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v5
      - uses: actions/setup-node@v3
        with:
          node-version: 22.x
          cache: "npm"

      - name: Cache preloaded images
        uses: actions/cache@v4
        with:
          path: .cache
          key: cache-${{ github.run_id }}
          restore-keys: |
            cache-
      - run: npm ci
      - run: npm run build

      - name: Deploy
        uses: cloudflare/wrangler-action@v3
        with:
          apiToken: ${{ secrets.CLOUDFLARE_API_TOKEN }}
          accountId: ${{ secrets.CLOUDFLARE_ACCOUNT_ID }}
          command: pages deploy ./dist --project-name=wallery
